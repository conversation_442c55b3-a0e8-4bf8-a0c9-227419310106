{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Start From Magento Big File New Products With SKU ON IT"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "sku", "rawType": "object", "type": "string"}, {"name": "mpn", "rawType": "object", "type": "string"}, {"name": "vendor_code", "rawType": "object", "type": "string"}, {"name": "la_id", "rawType": "int64", "type": "integer"}, {"name": "upc", "rawType": "int64", "type": "integer"}, {"name": "dn", "rawType": "float64", "type": "float"}, {"name": "imap", "rawType": "float64", "type": "float"}, {"name": "map_enabled", "rawType": "object", "type": "string"}, {"name": "price", "rawType": "float64", "type": "float"}, {"name": "msrp", "rawType": "float64", "type": "float"}, {"name": "map_price", "rawType": "float64", "type": "float"}, {"name": "attribute_set_code", "rawType": "object", "type": "string"}, {"name": "product_type", "rawType": "object", "type": "string"}, {"name": "product_websites", "rawType": "object", "type": "string"}, {"name": "product_online", "rawType": "int64", "type": "integer"}, {"name": "tax_class_name", "rawType": "object", "type": "string"}, {"name": "visibility", "rawType": "object", "type": "string"}, {"name": "google_product_type", "rawType": "object", "type": "string"}, {"name": "categories", "rawType": "object", "type": "string"}, {"name": "baseCat", "rawType": "object", "type": "string"}, {"name": "baseType", "rawType": "object", "type": "string"}, {"name": "manufacture_category", "rawType": "object", "type": "string"}, {"name": "producttype", "rawType": "object", "type": "string"}, {"name": "ProductName", "rawType": "object", "type": "string"}, {"name": "name", "rawType": "object", "type": "string"}, {"name": "name2", "rawType": "object", "type": "string"}, {"name": "url_key", "rawType": "object", "type": "string"}, {"name": "meta_title", "rawType": "object", "type": "string"}, {"name": "meta_description", "rawType": "object", "type": "string"}, {"name": "short_description", "rawType": "object", "type": "string"}, {"name": "LightsAmericaFinish", "rawType": "object", "type": "string"}, {"name": "manufacturer_finish", "rawType": "object", "type": "string"}, {"name": "finish", "rawType": "object", "type": "string"}, {"name": "finish_filter", "rawType": "object", "type": "string"}, {"name": "bulb_included", "rawType": "object", "type": "string"}, {"name": "number_of_bulbs", "rawType": "int64", "type": "integer"}, {"name": "light_count", "rawType": "object", "type": "string"}, {"name": "type_of_bulbs", "rawType": "object", "type": "string"}, {"name": "type_of_bulbs2", "rawType": "object", "type": "string"}, {"name": "lumens", "rawType": "object", "type": "string"}, {"name": "color_temp", "rawType": "object", "type": "string"}, {"name": "cri", "rawType": "object", "type": "string"}, {"name": "wire", "rawType": "object", "type": "string"}, {"name": "chain", "rawType": "object", "type": "string"}, {"name": "rod", "rawType": "object", "type": "string"}, {"name": "height", "rawType": "float64", "type": "float"}, {"name": "length", "rawType": "float64", "type": "float"}, {"name": "width", "rawType": "float64", "type": "float"}, {"name": "weight", "rawType": "float64", "type": "float"}, {"name": "brand", "rawType": "object", "type": "string"}, {"name": "collection_name", "rawType": "object", "type": "string"}, {"name": "country_of_manufacture", "rawType": "object", "type": "string"}, {"name": "max_wattage", "rawType": "float64", "type": "float"}, {"name": "voltage", "rawType": "object", "type": "string"}, {"name": "description", "rawType": "object", "type": "string"}, {"name": "style", "rawType": "object", "type": "string"}, {"name": "w_d_rated", "rawType": "object", "type": "string"}, {"name": "safety_rating", "rawType": "object", "type": "string"}, {"name": "material", "rawType": "object", "type": "string"}, {"name": "glass", "rawType": "object", "type": "string"}, {"name": "warranty", "rawType": "object", "type": "string"}, {"name": "meta_keywords", "rawType": "object", "type": "string"}, {"name": "cost", "rawType": "float64", "type": "float"}, {"name": "sku_group", "rawType": "object", "type": "string"}, {"name": "rooms", "rawType": "object", "type": "string"}, {"name": "ranking", "rawType": "object", "type": "string"}, {"name": "news_from_date", "rawType": "object", "type": "string"}, {"name": "news_to_date", "rawType": "object", "type": "string"}, {"name": "Active?", "rawType": "object", "type": "string"}, {"name": "qty", "rawType": "int64", "type": "integer"}, {"name": "Status Date", "rawType": "object", "type": "string"}, {"name": "ShippedVia", "rawType": "object", "type": "string"}, {"name": "fan_rpm", "rawType": "object", "type": "string"}, {"name": "f_airflow", "rawType": "object", "type": "string"}, {"name": "f_efficiency", "rawType": "object", "type": "string"}, {"name": "blade_pitch", "rawType": "object", "type": "string"}, {"name": "blade_span", "rawType": "object", "type": "string"}, {"name": "blade_finish", "rawType": "object", "type": "string"}, {"name": "blade_qty", "rawType": "object", "type": "string"}, {"name": "f_speeds", "rawType": "object", "type": "string"}, {"name": "electricity_usage_watts", "rawType": "object", "type": "string"}, {"name": "fan_light_kit", "rawType": "object", "type": "string"}, {"name": "fan_downrod", "rawType": "object", "type": "string"}, {"name": "Image File Name", "rawType": "object", "type": "string"}, {"name": "Image", "rawType": "object", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "rawType": "object", "type": "string"}, {"name": "Small Thumbnail", "rawType": "object", "type": "string"}, {"name": "Alt Image 1", "rawType": "object", "type": "string"}, {"name": "Alt Image 2", "rawType": "object", "type": "string"}, {"name": "Alt Image 3", "rawType": "object", "type": "string"}, {"name": "Alt Image 4", "rawType": "object", "type": "string"}, {"name": "Alt Image 5", "rawType": "object", "type": "string"}, {"name": "Alt Image 6", "rawType": "object", "type": "string"}, {"name": "Alt Image 7", "rawType": "object", "type": "string"}, {"name": "Alt Image 8", "rawType": "object", "type": "string"}, {"name": "Alt Image 9", "rawType": "object", "type": "string"}, {"name": "Alt Image 10", "rawType": "object", "type": "string"}, {"name": "vendor_instock_date", "rawType": "object", "type": "string"}, {"name": "Dark_Sky", "rawType": "object", "type": "string"}, {"name": "ada", "rawType": "object", "type": "string"}, {"name": "energy_star", "rawType": "object", "type": "string"}, {"name": "glass2", "rawType": "object", "type": "string"}, {"name": "height_filter", "rawType": "object", "type": "string"}, {"name": "width_filter", "rawType": "object", "type": "string"}, {"name": "price_filter", "rawType": "object", "type": "string"}, {"name": "canopy", "rawType": "object", "type": "string"}, {"name": "dimmable", "rawType": "object", "type": "string"}, {"name": "glass_filter", "rawType": "object", "type": "string"}, {"name": "shipping_width", "rawType": "float64", "type": "float"}, {"name": "shipping_height", "rawType": "float64", "type": "float"}, {"name": "shipping_length", "rawType": "float64", "type": "float"}, {"name": "shipping_weight", "rawType": "float64", "type": "float"}, {"name": "extension", "rawType": "object", "type": "string"}, {"name": "crystal", "rawType": "object", "type": "string"}], "ref": "83e3ad12-6244-47b1-a9c6-77a9b1581b0f", "rows": [["0", "N7345-790-174M", "N7345-790", "-174M", "4684194", "840254051195", "722.0", "1589.0", "Y", "1589.0", "2166.0", "1589.0", "<PERSON><PERSON><PERSON>", "simple", "base", "1", "Taxable Goods", "Catalog, Search", "", "Pendants", "Pendants", "Drum Shade", "", "Drum Shade", "Five Light Pendant", "Splendour 5-Light  in Brass - Antique", "Splendour 5-Light Five Light Pendant in Aged Antique Brass", "", "Splendour 5-Light  in Brass - Antique", "This 5-Light Five Light Pendant from Metropolitan is part of the Splendour collection and comes in a Aged Antique Brass finish.", "This 5-Light from Metropolitan is part of the Splendour collection. It comes in a Aged Antique Brass finish.<UL><LI> Measures 20.50\" H x 24.38\" L x 24.38\" W</LI><LI>UL certified. </LI><LI>Damp rated.</LI>", "Brass - Antique", "Aged Antique Brass", "Aged Antique Brass", "", "", "5", "", "B10.5", "Candelabra", "", "", "", "", "", "", "20.5", "24.38", "24.38", "16.82", "Metropolitan", "Splendour", "", "60.0", "", "", "Contemporary Modern, Mid-Century Modern", "<PERSON><PERSON>", "UL", "Iron / Fabric", "<PERSON><PERSON><PERSON>, Iron", "", "", "722.0", "N7345_Metropolitan", "", "", "", "", "Yes", "24", "6/30/2021", "UPS", "", "", "", "", "", "", "", "", "", "", "", "1157951.jpg", "https://cdn.lightsamerica.com/images/1157951.jpg", "https://cdn.lightsamerica.com/images/SM5/1157951.jpg", "https://cdn.lightsamerica.com/images/SM3/1157951.jpg", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "<PERSON>/<PERSON>", "19\" - 24\"", "19\" - 24\"", "$1500 & more", "6\"x1.13\"", "Yes", "<PERSON>/<PERSON>", "28.63", "14.0", "28.63", "23.37", "", ""]], "shape": {"columns": 114, "rows": 1}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sku</th>\n", "      <th>mpn</th>\n", "      <th>vendor_code</th>\n", "      <th>la_id</th>\n", "      <th>upc</th>\n", "      <th>dn</th>\n", "      <th>imap</th>\n", "      <th>map_enabled</th>\n", "      <th>price</th>\n", "      <th>msrp</th>\n", "      <th>...</th>\n", "      <th>price_filter</th>\n", "      <th>canopy</th>\n", "      <th>dimmable</th>\n", "      <th>glass_filter</th>\n", "      <th>shipping_width</th>\n", "      <th>shipping_height</th>\n", "      <th>shipping_length</th>\n", "      <th>shipping_weight</th>\n", "      <th>extension</th>\n", "      <th>crystal</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>N7345-790-174M</td>\n", "      <td>N7345-790</td>\n", "      <td>-174M</td>\n", "      <td>4684194</td>\n", "      <td>840254051195</td>\n", "      <td>722.0</td>\n", "      <td>1589.0</td>\n", "      <td>Y</td>\n", "      <td>1589.0</td>\n", "      <td>2166.0</td>\n", "      <td>...</td>\n", "      <td>$1500 &amp; more</td>\n", "      <td>6\"x1.13\"</td>\n", "      <td>Yes</td>\n", "      <td><PERSON>/<PERSON></td>\n", "      <td>28.63</td>\n", "      <td>14.0</td>\n", "      <td>28.63</td>\n", "      <td>23.37</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1 rows × 114 columns</p>\n", "</div>"], "text/plain": ["              sku        mpn vendor_code    la_id           upc     dn  \\\n", "0  N7345-790-174M  N7345-790       -174M  4684194  840254051195  722.0   \n", "\n", "     imap map_enabled   price    msrp  ...  price_filter    canopy dimmable  \\\n", "0  1589.0           Y  1589.0  2166.0  ...  $1500 & more  6\"x1.13\"      Yes   \n", "\n", "  glass_filter  shipping_width shipping_height shipping_length  \\\n", "0  <PERSON>           28.63            14.0           28.63   \n", "\n", "  shipping_weight extension crystal  \n", "0           23.37                    \n", "\n", "[1 rows x 114 columns]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["from my_pymodules.product_description import create_description\n", "from datetime import datetime, timedelta\n", "import pandas as pd\n", "import numpy as np\n", "import re\n", "\n", "df = pd.read_csv(r\"C:\\Users\\<USER>\\Desktop\\anthony_item_to_add_request.csv\", engine = \"pyarrow\", keep_default_na = False)\n", "\n", "df = df.rename(columns = {\"Product Name\" : \"ProductName\", \"Shipped Via\" : \"ShippedVia\", \"LightsAmerica Finish\" : \"LightsAmericaFinish\", \"family\": \"google_product_type\"})\n", "\n", "df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["INITIAL DROPS"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "sku", "rawType": "object", "type": "string"}, {"name": "mpn", "rawType": "object", "type": "string"}, {"name": "vendor_code", "rawType": "object", "type": "string"}, {"name": "la_id", "rawType": "int64", "type": "integer"}, {"name": "upc", "rawType": "int64", "type": "integer"}, {"name": "dn", "rawType": "float64", "type": "float"}, {"name": "imap", "rawType": "float64", "type": "float"}, {"name": "map_enabled", "rawType": "object", "type": "string"}, {"name": "price", "rawType": "float64", "type": "float"}, {"name": "msrp", "rawType": "float64", "type": "float"}, {"name": "map_price", "rawType": "float64", "type": "float"}, {"name": "attribute_set_code", "rawType": "object", "type": "string"}, {"name": "product_type", "rawType": "object", "type": "string"}, {"name": "product_websites", "rawType": "object", "type": "string"}, {"name": "product_online", "rawType": "int64", "type": "integer"}, {"name": "tax_class_name", "rawType": "object", "type": "string"}, {"name": "visibility", "rawType": "object", "type": "string"}, {"name": "google_product_type", "rawType": "object", "type": "string"}, {"name": "categories", "rawType": "object", "type": "string"}, {"name": "baseCat", "rawType": "object", "type": "string"}, {"name": "baseType", "rawType": "object", "type": "string"}, {"name": "manufacture_category", "rawType": "object", "type": "string"}, {"name": "producttype", "rawType": "object", "type": "string"}, {"name": "ProductName", "rawType": "object", "type": "string"}, {"name": "name", "rawType": "object", "type": "string"}, {"name": "name2", "rawType": "object", "type": "string"}, {"name": "url_key", "rawType": "object", "type": "string"}, {"name": "meta_title", "rawType": "object", "type": "string"}, {"name": "meta_description", "rawType": "object", "type": "string"}, {"name": "short_description", "rawType": "object", "type": "string"}, {"name": "LightsAmericaFinish", "rawType": "object", "type": "string"}, {"name": "manufacturer_finish", "rawType": "object", "type": "string"}, {"name": "finish", "rawType": "object", "type": "string"}, {"name": "finish_filter", "rawType": "object", "type": "string"}, {"name": "bulb_included", "rawType": "object", "type": "string"}, {"name": "number_of_bulbs", "rawType": "int64", "type": "integer"}, {"name": "light_count", "rawType": "object", "type": "string"}, {"name": "type_of_bulbs", "rawType": "object", "type": "string"}, {"name": "type_of_bulbs2", "rawType": "object", "type": "string"}, {"name": "lumens", "rawType": "object", "type": "string"}, {"name": "color_temp", "rawType": "object", "type": "string"}, {"name": "cri", "rawType": "object", "type": "string"}, {"name": "wire", "rawType": "object", "type": "string"}, {"name": "chain", "rawType": "object", "type": "string"}, {"name": "rod", "rawType": "object", "type": "string"}, {"name": "height", "rawType": "float64", "type": "float"}, {"name": "length", "rawType": "float64", "type": "float"}, {"name": "width", "rawType": "float64", "type": "float"}, {"name": "weight", "rawType": "float64", "type": "float"}, {"name": "brand", "rawType": "object", "type": "string"}, {"name": "collection_name", "rawType": "object", "type": "string"}, {"name": "country_of_manufacture", "rawType": "object", "type": "string"}, {"name": "max_wattage", "rawType": "float64", "type": "float"}, {"name": "voltage", "rawType": "object", "type": "string"}, {"name": "description", "rawType": "object", "type": "string"}, {"name": "style", "rawType": "object", "type": "string"}, {"name": "w_d_rated", "rawType": "object", "type": "string"}, {"name": "safety_rating", "rawType": "object", "type": "string"}, {"name": "material", "rawType": "object", "type": "string"}, {"name": "glass", "rawType": "object", "type": "string"}, {"name": "warranty", "rawType": "object", "type": "string"}, {"name": "meta_keywords", "rawType": "object", "type": "string"}, {"name": "cost", "rawType": "float64", "type": "float"}, {"name": "sku_group", "rawType": "object", "type": "string"}, {"name": "rooms", "rawType": "object", "type": "string"}, {"name": "ranking", "rawType": "object", "type": "string"}, {"name": "news_from_date", "rawType": "object", "type": "string"}, {"name": "news_to_date", "rawType": "object", "type": "string"}, {"name": "Active?", "rawType": "object", "type": "string"}, {"name": "qty", "rawType": "int64", "type": "integer"}, {"name": "Status Date", "rawType": "object", "type": "string"}, {"name": "ShippedVia", "rawType": "object", "type": "string"}, {"name": "fan_rpm", "rawType": "object", "type": "string"}, {"name": "f_airflow", "rawType": "object", "type": "string"}, {"name": "f_efficiency", "rawType": "object", "type": "string"}, {"name": "blade_pitch", "rawType": "object", "type": "string"}, {"name": "blade_span", "rawType": "object", "type": "string"}, {"name": "blade_finish", "rawType": "object", "type": "string"}, {"name": "blade_qty", "rawType": "object", "type": "string"}, {"name": "f_speeds", "rawType": "object", "type": "string"}, {"name": "electricity_usage_watts", "rawType": "object", "type": "string"}, {"name": "fan_light_kit", "rawType": "object", "type": "string"}, {"name": "fan_downrod", "rawType": "object", "type": "string"}, {"name": "Image File Name", "rawType": "object", "type": "string"}, {"name": "Image", "rawType": "object", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "rawType": "object", "type": "string"}, {"name": "Small Thumbnail", "rawType": "object", "type": "string"}, {"name": "Alt Image 1", "rawType": "object", "type": "string"}, {"name": "Alt Image 2", "rawType": "object", "type": "string"}, {"name": "Alt Image 3", "rawType": "object", "type": "string"}, {"name": "Alt Image 4", "rawType": "object", "type": "string"}, {"name": "Alt Image 5", "rawType": "object", "type": "string"}, {"name": "Alt Image 6", "rawType": "object", "type": "string"}, {"name": "Alt Image 7", "rawType": "object", "type": "string"}, {"name": "Alt Image 8", "rawType": "object", "type": "string"}, {"name": "Alt Image 9", "rawType": "object", "type": "string"}, {"name": "Alt Image 10", "rawType": "object", "type": "string"}, {"name": "vendor_instock_date", "rawType": "object", "type": "string"}, {"name": "Dark_Sky", "rawType": "object", "type": "string"}, {"name": "ada", "rawType": "object", "type": "string"}, {"name": "energy_star", "rawType": "object", "type": "string"}, {"name": "glass2", "rawType": "object", "type": "string"}, {"name": "height_filter", "rawType": "object", "type": "string"}, {"name": "width_filter", "rawType": "object", "type": "string"}, {"name": "price_filter", "rawType": "object", "type": "string"}, {"name": "canopy", "rawType": "object", "type": "string"}, {"name": "dimmable", "rawType": "object", "type": "string"}, {"name": "glass_filter", "rawType": "object", "type": "string"}, {"name": "shipping_width", "rawType": "float64", "type": "float"}, {"name": "shipping_height", "rawType": "float64", "type": "float"}, {"name": "shipping_length", "rawType": "float64", "type": "float"}, {"name": "shipping_weight", "rawType": "float64", "type": "float"}, {"name": "extension", "rawType": "object", "type": "string"}, {"name": "crystal", "rawType": "object", "type": "string"}], "ref": "35fe25ae-394c-4d24-949b-b578ef1971a4", "rows": [["0", "N7345-790-174M", "N7345-790", "-174M", "4684194", "840254051195", "722.0", "1589.0", "Y", "1589.0", "2166.0", "1589.0", "<PERSON><PERSON><PERSON>", "simple", "base", "1", "Taxable Goods", "Catalog, Search", "", "Pendants", "Pendants", "Drum Shade", "", "Drum Shade", "Five Light Pendant", "Splendour 5-Light  in Brass - Antique", "Splendour 5-Light Five Light Pendant in Aged Antique Brass", "", "Splendour 5-Light  in Brass - Antique", "This 5-Light Five Light Pendant from Metropolitan is part of the Splendour collection and comes in a Aged Antique Brass finish.", "This 5-Light from Metropolitan is part of the Splendour collection. It comes in a Aged Antique Brass finish.<UL><LI> Measures 20.50\" H x 24.38\" L x 24.38\" W</LI><LI>UL certified. </LI><LI>Damp rated.</LI>", "Brass - Antique", "Aged Antique Brass", "Aged Antique Brass", "", "", "5", "", "B10.5", "Candelabra", "", "", "", "", "", "", "20.5", "24.38", "24.38", "16.82", "Metropolitan", "Splendour", "", "60.0", "", "", "Contemporary Modern, Mid-Century Modern", "<PERSON><PERSON>", "UL", "Iron / Fabric", "<PERSON><PERSON><PERSON>, Iron", "", "", "722.0", "N7345_Metropolitan", "", "", "", "", "Yes", "24", "6/30/2021", "UPS", "", "", "", "", "", "", "", "", "", "", "", "1157951.jpg", "https://cdn.lightsamerica.com/images/1157951.jpg", "https://cdn.lightsamerica.com/images/SM5/1157951.jpg", "https://cdn.lightsamerica.com/images/SM3/1157951.jpg", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "<PERSON>/<PERSON>", "19\" - 24\"", "19\" - 24\"", "$1500 & more", "6\"x1.13\"", "Yes", "<PERSON>/<PERSON>", "28.63", "14.0", "28.63", "23.37", "", ""]], "shape": {"columns": 114, "rows": 1}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sku</th>\n", "      <th>mpn</th>\n", "      <th>vendor_code</th>\n", "      <th>la_id</th>\n", "      <th>upc</th>\n", "      <th>dn</th>\n", "      <th>imap</th>\n", "      <th>map_enabled</th>\n", "      <th>price</th>\n", "      <th>msrp</th>\n", "      <th>...</th>\n", "      <th>price_filter</th>\n", "      <th>canopy</th>\n", "      <th>dimmable</th>\n", "      <th>glass_filter</th>\n", "      <th>shipping_width</th>\n", "      <th>shipping_height</th>\n", "      <th>shipping_length</th>\n", "      <th>shipping_weight</th>\n", "      <th>extension</th>\n", "      <th>crystal</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>N7345-790-174M</td>\n", "      <td>N7345-790</td>\n", "      <td>-174M</td>\n", "      <td>4684194</td>\n", "      <td>840254051195</td>\n", "      <td>722.0</td>\n", "      <td>1589.0</td>\n", "      <td>Y</td>\n", "      <td>1589.0</td>\n", "      <td>2166.0</td>\n", "      <td>...</td>\n", "      <td>$1500 &amp; more</td>\n", "      <td>6\"x1.13\"</td>\n", "      <td>Yes</td>\n", "      <td><PERSON>/<PERSON></td>\n", "      <td>28.63</td>\n", "      <td>14.0</td>\n", "      <td>28.63</td>\n", "      <td>23.37</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1 rows × 114 columns</p>\n", "</div>"], "text/plain": ["              sku        mpn vendor_code    la_id           upc     dn  \\\n", "0  N7345-790-174M  N7345-790       -174M  4684194  840254051195  722.0   \n", "\n", "     imap map_enabled   price    msrp  ...  price_filter    canopy dimmable  \\\n", "0  1589.0           Y  1589.0  2166.0  ...  $1500 & more  6\"x1.13\"      Yes   \n", "\n", "  glass_filter  shipping_width shipping_height shipping_length  \\\n", "0  <PERSON>           28.63            14.0           28.63   \n", "\n", "  shipping_weight extension crystal  \n", "0           23.37                    \n", "\n", "[1 rows x 114 columns]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["df = df[df[\"Image\"] != \"\"]\n", "df = df[(df[\"Active?\"] == \"Yes\")]\n", "df = df[df[\"qty\"] > 0]\n", "\n", "#df['voltage'] = pd.to_numeric(df['voltage'], errors='coerce')\n", "#df = df[df[\"voltage\"].astype(float) <= 120]\n", "\n", "df['cost'] = pd.to_numeric(df['cost'], errors='coerce')\n", "df = df[~((df['ShippedVia'].str.upper().str.contains('TRK') | \n", "           df['ShippedVia'].str.upper().str.contains('LTL') | \n", "           df['ShippedVia'].str.contains('Truck') | \n", "           df['ShippedVia'].str.contains('LTL') | \n", "           df['ShippedVia'].str.contains('MF') | \n", "           df['ShippedVia'].str.contains('Freight') | \n", "           df['ShippedVia'].str.contains('Furniture') | \n", "           df['ShippedVia'].str.contains('Glove')) & \n", "          (df['cost'] < 2000))]\n", "\n", "df = df[~(df['manufacture_category'].str.contains('|'.join(['Shop Light', 'Bulb', 'Dimmer', 'Drill', 'Driver', 'Pro'])) | \n", "          (df['ProductName'] == 'Light Bulb'))]\n", "\n", "df['dn'] = pd.to_numeric(df['dn'], errors='coerce')\n", "df = df[df[\"dn\"] >= 20]\n", "\n", "df[\"imap\"] = df.apply(lambda row: row[\"dn\"] * 2 if row[\"imap\"] == \"\" else row[\"imap\"], axis=1)\n", "\n", "df[\"price\"] = df[\"imap\"]\n", "df[\"map_price\"] = df[\"imap\"]\n", "df[\"cost\"] = df[\"dn\"]\n", "\n", "df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["FINISH AND FINISH FILTER CLEAN UP"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["# replace finish = LightsAmericaFinish if finish == \"\"\n", "df.loc[df['finish'] == \"\", 'finish'] = df['LightsAmericaFinish']\n", "\n", "# drop if finish == \"\"\n", "df = df[df['finish'] != \"\"]\n", "\n", "# rest of the replace statements\n", "df['finish'] = df['finish'].str.replace(\" With \", \" with \")\n", "df['finish'] = df['finish'].str.replace(\" And \", \" and \")\n", "df['finish'] = df['finish'].str.replace(\" On Metal\", \"\")\n", "df['finish'] = df['finish'].str.replace(\" with Clear Glass\", \"\")\n", "df['finish'] = df['finish'].str.replace(\" with Clear Seedy Glass\", \"\")\n", "df['finish'] = df['finish'].str.replace(\" with Half Opal Glass\", \"\")\n", "df['finish'] = df['finish'].str.replace(\" with True Opal Glass\", \"\")\n", "df['finish'] = df['finish'].str.replace(\" and True Opal Glass\", \"\")\n", "df['finish'] = df['finish'].str.replace(\" with Ribbed Opal Glass\", \"\")\n", "df['finish'] = df['finish'].str.replace(\" with Opal Glass\", \"\")\n", "df['finish'] = df['finish'].str.replace(\" and Opal Glass\", \"\")\n", "df['finish'] = df['finish'].str.replace(\" with Honeycomb Glass\", \"\")\n", "df['finish'] = df['finish'].str.replace(\" with Ripple Glass Glass\", \"\")\n", "df['finish'] = df['finish'].str.replace(\" with Ripple Glass\", \"\")\n", "df['finish'] = df['finish'].str.replace(\" with Sandblasted Seedy Glass\", \"\")\n", "df['finish'] = df['finish'].str.replace(\" with Crackle Glass\", \"\")\n", "df['finish'] = df['finish'].str.replace(\" with Hammered Clear Glass\", \"\")\n", "df['finish'] = df['finish'].str.replace(\" with Ribbed Half Opal Glass\", \"\")\n", "df['finish'] = df['finish'].str.replace(\" with Ribbed <PERSON> Glass\", \"\")\n", "df['finish'] = df['finish'].str.replace(\" with Vodka Ice Glass\", \"\")\n", "df['finish'] = df['finish'].str.replace(\" with Silk Screened White Glass\", \"\")\n", "df['finish'] = df['finish'].str.replace(\" with Silk Screened Opal Glass\", \"\")\n", "df['finish'] = df['finish'].str.replace(\" with Silk Screen Opal Glass\", \"\")\n", "df['finish'] = df['finish'].str.replace(\" with Artisinal Water Glass\", \"\")\n", "df['finish'] = df['finish'].str.replace(\" with Smoke Glass\", \"\")\n", "df['finish'] = df['finish'].str.replace(\"<PERSON><PERSON>\", \"Nickel\")\n", "df['finish'] = df['finish'].str.replace(\"/\", \" with \")\n", "df['finish'] = df['finish'].str.replace(\"  \", \" \")\n", "\n", "df.loc[(df['finish'] == \"Aged Brass Aged Brass\") | (df['finish'] == \"Aged Brass with Aged Brass\"), 'finish'] = \"Aged Brass\"\n", "df['finish'] = df['finish'].str.replace(\" W \", \" with \")\n", "df['finish'] = df['finish'].str.replace(\" w \", \" with \")\n", "df['finish'] = df['finish'].str.replace(\" with with \", \" with \")\n", "\n", "df.loc[(df['sku'].str.contains(\"AG\")) & (df['finish'].str.contains(\"|\") | df['finish'].str.contains(\",\")), 'finish'] = \"Aged Gold\"\n", "df.loc[(df['sku'].str.contains(\"MB\")) & (df['finish'].str.contains(\"|\") | df['finish'].str.contains(\",\")), 'finish'] = \"Matte Black\"\n", "df.loc[(df['sku'].str.contains(\"BN\")) & (df['finish'].str.contains(\"|\") | df['finish'].str.contains(\",\")), 'finish'] = \"Brushed Nickel\"\n", "df.loc[(df['sku'].str.contains(\"BK\")) & (df['finish'].str.contains(\"|\") | df['finish'].str.contains(\",\")), 'finish'] = \"Black\"\n", "df.loc[(df['sku'].str.contains(\"BG\")) & (df['finish'].str.contains(\"|\") | df['finish'].str.contains(\",\")), 'finish'] = \"Brushed Gold\"\n", "df.loc[(df['sku'].str.contains(\"SL\")) & (df['finish'].str.contains(\"|\") | df['finish'].str.contains(\",\")), 'finish'] = \"Steel\"\n", "df.loc[(df['sku'].str.contains(\"CH\")) & (df['finish'].str.contains(\"|\") | df['finish'].str.contains(\",\")), 'finish'] = \"Chrome\"\n", "df.loc[(df['sku'].str.contains(\"GY\")) & (df['finish'].str.contains(\"|\") | df['finish'].str.contains(\",\")), 'finish'] = \"Gray\"\n", "df.loc[(df['sku'].str.contains(\"BZ\")) & (df['finish'].str.contains(\"|\") | df['finish'].str.contains(\",\")), 'finish'] = \"Bronze\"\n", "df.loc[(df['sku'].str.contains(\"WH\")) & (df['finish'].str.contains(\"|\") | df['finish'].str.contains(\",\")), 'finish'] = \"White\"\n", "df.loc[(df['sku'].str.contains(\"WB\")) & (df['finish'].str.contains(\"|\") | df['finish'].str.contains(\",\")), 'finish'] = \"Warm Bronze\"\n", "df.loc[(df['sku'].str.contains(\"GH\")) & (df['finish'].str.contains(\"|\") | df['finish'].str.contains(\",\")), 'finish'] = \"Graphite\"\n", "df.loc[(df['sku'].str.contains(\"CG\")) & (df['finish'].str.contains(\"|\") | df['finish'].str.contains(\",\")), 'finish'] = \"Champagne\"\n", "df.loc[(df['sku'].str.contains(\"PN\")) & (df['finish'].str.contains(\"|\") | df['finish'].str.contains(\",\")), 'finish'] = \"Polished Nickel\"\n", "df.loc[df['finish'].str.lower() == \"brz\", 'finish'] = \"Bronze\"\n", "df.loc[df['finish'].str.lower() == \"blk\", 'finish'] = \"Black\"\n", "df.loc[(df['sku'].str.contains(\"SS\")) & df['finish'].str.contains(\"|\"), 'finish'] = \"Stainless Steel\"\n", "df.loc[(df['sku'].str.contains(\"MBAG\")) | (df['sku'].str.contains(\"AGMB\")), 'finish'] = \"Aged Gold with <PERSON><PERSON>\"\n", "df.loc[(df['sku'].str.contains(\"WHAG\")) | (df['sku'].str.contains(\"AGWH\")), 'finish'] = \"Aged Gold with White\"\n", "df.loc[df['sku'].str.contains(\"AGWT\"), 'finish'] = \"Aged Gold with Walnut\"\n", "df.loc[df['sku'].str.contains(\"MBWT\"), 'finish'] = \"<PERSON><PERSON> with Walnut\"\n", "df.loc[df['sku'].str.contains(\"WHWK\"), 'finish'] = \"White with White Oak\"\n", "df.loc[df['sku'].str.contains(\"AGWL\"), 'finish'] = \"Aged Gold with White Linen\"\n", "df.loc[df['sku'].str.contains(\"MBWL\"), 'finish'] = \"<PERSON><PERSON> with <PERSON>\"\n", "df.loc[df['sku'].str.contains(\"AGMB\"), 'finish'] = \"Aged Gold with <PERSON><PERSON>\"\n", "df.loc[df['sku'].str.contains(\"MBAG\"), 'finish'] = \"<PERSON><PERSON> Black with Aged Gold\"\n", "\n", "df['finish'] = df['finish'].str.replace(\" n with a \", \"\")\n", "df['finish'] = df['finish'].str.replace(\" & \", \" with \")\n", "df['finish'] = df['finish'].str.replace(\" and \", \" with \")\n", "df['finish'] = df['finish'].str.replace(\" And \", \" with \")\n", "df['finish'] = df['finish'].str.replace(\"Black with Black\", \"Black\")\n", "\n", "df.loc[df['sku'].str.contains(\"BK-GD\"), 'finish'] = \"Black with Gold\"\n", "df.loc[df['sku'].str.contains(\"WH-GD\"), 'finish'] = \"White with Gold\"\n", "df.loc[df['sku'].str.contains(\"WH-SV\"), 'finish'] = \"White with Silver\"\n", "df.loc[df['sku'].str.contains(\"BK-GL\"), 'finish'] = \"Black with Gold Leaf\"\n", "df.loc[df['sku'].str.contains(\"BK-CP\"), 'finish'] = \"Black with Copper\"\n", "df.loc[df['sku'].str.contains(\"BG-CP\"), 'finish'] = \"Brushed Gold with Copper\"\n", "df.loc[df['sku'].str.contains(\"BK-BG\"), 'finish'] = \"Black with Brushed Gold\"\n", "df.loc[(df['finish'].str.contains(\"Matte Black\")) & (df['finish'].str.contains(\"Textured\")), 'finish'] = \"Textured Matte Black\"\n", "df.loc[df['sku'].str.contains(\"UBMS\"), 'finish'] = \"Urban Bronze with a Metal Shade\"\n", "\n", "df['finish'] = df['finish'].str.replace(\"WZC\", \"Weathered Zinc\")\n", "df['finish'] = df['finish'].str.replace(\"Zinc+\", \"Zinc + \")\n", "df['finish'] = df['finish'].str.replace(\"ATB\", \"Antique Brass\")\n", "df['finish'] = df['finish'].str.replace(\"  \", \" \")\n", "\n", "df.loc[df['finish'].str.lower().str.contains(\"black\"), 'finish_filter'] = \"Black\"\n", "df.loc[df['finish'].str.lower().str.contains(\"gold\"), 'finish_filter'] = \"Gold\"\n", "df.loc[df['finish'].str.lower().str.contains(\"nickel\"), 'finish_filter'] = \"Nickel\"\n", "df.loc[df['finish'].str.lower().str.contains(\"brass\"), 'finish_filter'] = \"Brass\"\n", "df.loc[df['finish'].str.lower().str.contains(\"silver\"), 'finish_filter'] = \"Silver\"\n", "df.loc[df['finish'].str.lower().str.contains(\"white\"), 'finish_filter'] = \"White\"\n", "df.loc[df['finish'].str.lower().str.contains(\"bronze\"), 'finish_filter'] = \"Bronze\"\n", "df.loc[df['finish'].str.lower().str.contains(\"chrome\"), 'finish_filter'] = \"Chrome\"\n", "df.loc[df['finish'].str.lower().str.contains(\"brown\"), 'finish_filter'] = \"Brown\"\n", "df.loc[df['finish'].str.lower().str.contains(\"copper\"), 'finish_filter'] = \"Copper\"\n", "df.loc[df['finish'].str.lower().str.contains(\"crystal\"), 'finish_filter'] = \"Crystal\"\n", "df.loc[df['finish'].str.lower().str.contains(\"gray\"), 'finish_filter'] = \"Gray\"\n", "df.loc[df['finish'].str.lower().str.contains(\"iron\"), 'finish_filter'] = \"Iron\"\n", "df.loc[df['finish'].str.lower().str.contains(\"pewter\"), 'finish_filter'] = \"Pewter\"\n", "df.loc[df['finish'].str.lower().str.contains(\"rust\"), 'finish_filter'] = \"Rust\"\n", "df.loc[df['finish'].str.lower().str.contains(\"stainless steel\"), 'finish_filter'] = \"Stainless Steel\"\n", "df.loc[df['finish'].str.lower().str.contains(\"steel\"), 'finish_filter'] = \"Steel\"\n", "df.loc[df['finish'].str.lower().str.contains(\"wood\"), 'finish_filter'] = \"Wood\"\n", "df.loc[df['finish_filter'] == \"\", 'finish_filter'] = \"Other\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["BULBS FIXING"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["df[\"number_of_bulbs\"] = df.apply(lambda row: \"0\" if row[\"number_of_bulbs\"] == \"\" else row[\"number_of_bulbs\"], axis=1)\n", "\n", "df['bulb_included'] = df['bulb_included'].replace(\"\", \"No\")\n", "df['bulb_included'] = df['bulb_included'].replace({\"1\": \"Yes\", \"0\": \"No\"})\n", "\n", "df['type_of_bulbs2'] = df['type_of_bulbs2'].str.replace(\"PCB Integrated\", \"Integrated\").str.replace(\"PCB\", \"Integrated\")\n", "\n", "df['type_of_bulbs'] = df.apply(lambda row: row['type_of_bulbs'] + \" \" + row['type_of_bulbs2'] if row['type_of_bulbs'] != row['type_of_bulbs2'] and row['type_of_bulbs2'] != \"\" else row['type_of_bulbs'], axis=1)\n", "\n", "df['light_count'] = pd.to_numeric(df['number_of_bulbs'], errors='coerce')\n", "df['light_count'] = df['light_count'].apply(lambda x: '1-Light' if x == 1 else\n", "                                                      '2-Light' if x == 2 else\n", "                                                      '3-Light' if x == 3 else\n", "                                                      '4-Light' if x == 4 else\n", "                                                      '5-Light' if x == 5 else\n", "                                                      '6-7 Light' if 6 <= x <= 7 else\n", "                                                      '8-11 Light' if 8 <= x <= 11 else\n", "                                                      '12-15 Light' if 12 <= x <= 15 else\n", "                                                      '16+ Light' if x > 15 else '')\n", "\n", "df['light_count'] = df.apply(lambda row: \"1-Light\" if \"Integrated\" in row['type_of_bulbs'] and row['light_count'] == \"\" else row['light_count'], axis=1)\n", "\n", "df['max_wattage'] = pd.to_numeric(df['max_wattage'], errors='coerce')\n", "df['max_wattage'] = df['max_wattage'].apply(lambda x: round(x, 1) if not pd.isnull(x) else x)\n", "\n", "df['bulb_filter'] = df.apply(lambda row: \"Incandescent/LED\" if any(x in row['type_of_bulbs'] for x in [\"E26\", \"E12\", \"T45\", \"G80\", \"A19\", \"G9\", \"Medium\", \"Candelabra\", \"G4\", \"A-19\", \"G6.35\", \"GY6.35\", \"GU10\"]) else row.get('bulb_filter', ''), axis=1)\n", "df['bulb_filter'] = df.apply(lambda row: \"Integrated LED\" if any(x in row['type_of_bulbs'] for x in [\"LED\", \"Module\", \"Built-In\", \"Integrated\"]) else row['bulb_filter'], axis=1)\n", "\n", "df['bulb_included'] = df.apply(lambda row: \"Yes\" if row.get('bulb_filter', '') == \"Integrated LED\" else row['bulb_included'], axis=1)\n", "\n", "df[['height', 'length', 'width', 'weight', 'height_filter', 'width_filter']] = df[['height', 'length', 'width', 'weight', 'height_filter', 'width_filter']].replace(\"\", \"__EMPTY__VALUE__\")\n", "\n", "df['weight'] = pd.to_numeric(df['weight'], errors='coerce')\n", "df['weight'] = df['weight'].apply(lambda x: round(x, 2) if not pd.isnull(x) else x)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["COUNTRY OF MANUFACTURE AND MANUFACTURE CATEGORY"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["df['f_watts'] = df['electricity_usage_watts']\n", "df['airflow'] = df['f_airflow']\n", "df['airflow_efficiency'] = df['f_efficiency']\n", "df['blade_number'] = df['blade_qty']\n", "df['light_kit_included'] = df['fan_light_kit']\n", "df['blades_reversible'] = \"\"\n", "df['bulbs'] = df['number_of_bulbs']\n", "\n", "df['blade_finish'] = df['blade_finish'].astype(str)\n", "df.loc[(df['blade_finish'].str.contains('/')) | (df['blade_finish'].str.contains(' and ')), 'blades_reversible'] = \"Yes\"\n", "\n", "df['blade_span'] = df['blade_span'].astype(str)\n", "df['fan_downrod'] = df['fan_downrod'].str.replace(\"``\", \"\\\"\")\n", "df['fan_downrod'] = df['fan_downrod'].str.replace(\"''\", \"\\\"\")\n", "\n", "df.loc[(df['w_d_rated'].str.contains(\"W\") == False) & (df['w_d_rated'].str.contains(\"Da\") == False), 'w_d_rated'] = \"Dry\"\n", "df.loc[(df['w_d_rated'].str.contains(\"W\") == True) & (df['w_d_rated'].str.contains(\"D\") == False), 'w_d_rated'] = \"Wet\"\n", "df.loc[(df['w_d_rated'].str.contains(\"W\") == False) & (df['w_d_rated'].str.contains(\"Dr\") == False), 'w_d_rated'] = \"Damp\"\n", "\n", "df['warranty'] = df['warranty'].str.replace(\"/\", \"and\", 10)\n", "df['warranty'] = df['warranty'].str.replace(\"and\", \",\", 10)\n", "df.loc[df['warranty'] == \"N/A\", 'warranty'] = \"__EMPTY__VALUE__\"\n", "\n", "df.loc[(df['country_of_manufacture'].str.upper().str.contains(\"CHINA\") == True) | (df['country_of_manufacture'].str.upper().str.contains(\"CN\") == True), 'country_of_manufacture'] = \"China\"\n", "df.loc[(df['country_of_manufacture'].str.upper().str.contains(\"UNTIED STATES\") == True) | (df['country_of_manufacture'].str.upper().str.contains(\"US\") == True), 'country_of_manufacture'] = \"United States\"\n", "df.loc[(df['country_of_manufacture'].str.upper().str.contains(\"VIETNAM\") == True) | (df['country_of_manufacture'].str.upper().str.contains(\"VN\") == True), 'country_of_manufacture'] = \"Vietnam\"\n", "df.loc[(df['country_of_manufacture'].str.upper().str.contains(\"PHILIPPINES\") == True) | (df['country_of_manufacture'].str.upper().str.contains(\"PH\") == True), 'country_of_manufacture'] = \"Philippines\"\n", "df.loc[(df['country_of_manufacture'].str.upper().str.contains(\"TAIWAN\") == True) | (df['country_of_manufacture'].str.upper().str.contains(\"TW\") == True), 'country_of_manufacture'] = \"China\"\n", "df.loc[df['country_of_manufacture'].str.upper().str.contains(\"PRC\") == True, 'country_of_manufacture'] = \"China\"\n", "df.loc[df['country_of_manufacture'].str.upper().str.contains(\"IN\") == True, 'country_of_manufacture'] = \"India\"\n", "\n", "df['manufacture_category'] = df['manufacture_category'].str.replace(\" | \", \" \")\n", "\n", "df = df[~(\n", "        df['manufacture_category'].str.contains(\"Exit\") |\n", "        df['manufacture_category'].str.contains(\"Emergency\") |\n", "        df['manufacture_category'].str.contains(\"Medicine Cabinet\") |\n", "        df['manufacture_category'].str.contains(\"Ceiling Medallion\") |\n", "        df['manufacture_category'].str.contains(\"Shade Display Tree\") |\n", "        df['manufacture_category'].str.contains(\"Tape\") |\n", "        df['ProductName'].str.contains(\"Tape\") |\n", "        df['producttype'].str.contains(\"Tape\") |\n", "        df['manufacture_category'].str.contains(\"Mirror\") |\n", "        df['manufacture_category'].str.contains(\"Umbrella\")\n", "        )]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["CHAIN AND WIRE CLEANING"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["df['chain'] = df['chain'].str.replace('``', '').str.replace(\"'''\", '').str.replace('\"', '').str.upper()\n", "df['chain'] = df['chain'].str.replace('[A-Z/ ]', '', regex=True) # Remove all letters except numbers using re\n", "df['chain'] = df['chain'].str.replace('`', '\"')\n", "df['chain'] = df['chain'].replace(\"160\", \"60\")\n", "df['chain'] = df['chain'].replace(\"172\", \"72\")\n", "df['chain'] = df['chain'].replace(\"1120\", \"120\")\n", "\n", "df['rod'] = df['rod'].str.replace('`', '\"')\n", "\n", "df['wire'] = df['wire'].str.replace('``', '').str.replace(\"'''\", '').str.replace('\"', '').str.upper()\n", "df['wire'] = df['wire'].str.replace('[A-Z/ ]', '', regex=True) \n", "df['wire'] = df['wire'].str.replace('`', '\"')\n", "df['wire'] = df['wire'].replace(\"16\", \"6\")\n", "df['wire'] = df['wire'].replace(\"18\", \"8\")\n", "df['wire'] = df['wire'].replace(\"118\", \"18\")\n", "df['wire'] = df['wire'].replace(\"136\", \"36\")\n", "df['wire'] = df['wire'].replace(\"172\", \"72\")\n", "df['wire'] = df['wire'].replace(\"176\", \"76\")\n", "df['wire'] = df['wire'].replace(\"196\", \"96\")\n", "df['wire'] = df['wire'].replace(\"1120\", \"120\")\n", "df['wire'] = df['wire'].replace(\"1144\", \"144\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["GOOGLE PRODUCT TYPE"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["# ACCESSORY\n", "df.loc[(df['manufacture_category'].str.contains('Address')) | (df['ProductName'].str.contains('Address')), 'google_product_type'] = 'Accessory'\n", "df.loc[(df['manufacture_category'].str.contains('Chime')) | (df['producttype'].str.contains('Chime')), 'google_product_type'] = 'Door Chime'\n", "\n", "# BATH & VANITY\n", "df.loc[(df['categories'].str.contains('Bathroom')) | (df['ProductName'].str.contains('Bathroom')) | \n", "       (df['manufacture_category'].str.contains('Vanity')) | (df['producttype'].str.contains('Bath')), 'google_product_type'] = 'Bathroom Vanity Light'\n", "df.loc[df['manufacture_category'].str.contains('Bath'), 'google_product_type'] = 'Bathroom Vanity Light'\n", "df.loc[(df['categories'].str.contains('Bath')) | (df['ProductName'].str.contains('Bath')) | \n", "       (df['ProductName'].str.contains('Vanity')), 'google_product_type'] = 'Bathroom Vanity Light'\n", "\n", "# CEILING FANS\n", "df.loc[(df['categories'].str.contains('Fan')) & (df['producttype'].str.contains('Fandelier')), 'google_product_type'] = 'Fandelier'\n", "df.loc[(df['categories'].str.contains('Fan')) & (df['producttype'].str.contains('Hugger')), 'google_product_type'] = 'Hugger Ceiling Fan'\n", "df.loc[(df['manufacture_category'].str.contains('Remote')) | (df['producttype'].str.contains('Fan Control')) | \n", "       (df['manufacture_category'].str.contains('Fan Control')), 'google_product_type'] = 'Ceiling Fan Control'\n", "df.loc[(df['manufacture_category'].str.contains('Fitter')) | (df['manufacture_category'].str.contains('Light Kit')), 'google_product_type'] = 'Ceiling Fan Light Kit'\n", "df.loc[(df['producttype'].str.contains('Ceiling Fan')) & (df['google_product_type'].str.contains('Fan') == False), 'google_product_type'] = 'Hanging Ceiling Fan'\n", "df.loc[(df['manufacture_category'].str.contains('Ceiling Fan')) & (df['producttype'].str.contains('Damp Location')), 'google_product_type'] = 'Outdoor Ceiling Fan'\n", "df.loc[(df['categories'].str.contains('Fan')) & ((df['producttype'].str.contains('Damp')) | (df['producttype'].str.contains('Wet'))), 'google_product_type'] = 'Outdoor Ceiling Fan'\n", "df.loc[(df['manufacture_category'].str.contains('Ceiling Fan')) & (df['producttype'].str.contains('Wet Location')), 'google_product_type'] = 'Outdoor Ceiling Fan'\n", "df.loc[(df['categories'].str.contains('Fan')) & (df['ProductName'].str.contains('Wall')), 'google_product_type'] = 'Wall Fan'\n", "\n", "# CHANDELIERS\n", "df.loc[(df['manufacture_category'].str.contains('Chandelier') | \n", "        df['categories'].str.contains('Chandelier') | \n", "        df['ProductName'].str.contains('Chandel<PERSON>')) & \n", "       ~df['manufacture_category'].str.contains('Pendant'), 'google_product_type'] = 'Chandelier'\n", "\n", "df.loc[(df['manufacture_category'].str.contains('Linear') | \n", "        df['categories'].str.contains('Linear') | \n", "        df['ProductName'].str.contains('Linear')) & \n", "       ~df['producttype'].str.contains('Ceiling'), 'google_product_type'] = 'Linear Chandelier'\n", "\n", "df.loc[df['categories'].str.contains('Mini Chandelier'), 'google_product_type'] = 'Mini Chandelier'\n", "df.loc[df['manufacture_category'].str.contains('Island'), 'google_product_type'] = 'Linear Chandelier'\n", "\n", "# CLOSE-TO-CEILING\n", "df.loc[(df['categories'].str.contains('Flush Mount') | \n", "        df['manufacture_category'].str.contains('Flush Mount') | \n", "        df['ProductName'].str.contains('Flush Mount')) & \n", "       ~df['ProductName'].str.contains('Fan'), 'google_product_type'] = 'Flush Mount Ceiling Light'\n", "\n", "df.loc[(df['categories'].str.contains('Semi-Flush') | \n", "        df['manufacture_category'].str.contains('Semi-Flush') | \n", "        df['ProductName'].str.contains('Semi-Flush') | \n", "        df['manufacture_category'].str.contains('Semi ')), 'google_product_type'] = 'Semi-Flush Ceiling Light'\n", "\n", "df.loc[(df['manufacture_category'] == 'Close-to-Ceiling') & \n", "       (df['categories'] == 'Pendants'), 'google_product_type'] = 'Pendant'\n", "\n", "df.loc[(df['categories'].str.contains('Exterior')) & \n", "       (df['producttype'].str.contains('Mount')), 'google_product_type'] = 'Outdoor Ceiling Light'\n", "\n", "df.loc[(df['categories'].str.contains('Track')) | \n", "       (df['manufacture_category'].str.contains('Directional')), 'google_product_type'] = 'Track Light'\n", "\n", "df.loc[(df['manufacture_category'].str.contains('Dual Mount')) & \n", "       ((df['categories'].str.contains('Semi')) | \n", "        (df['categories'].str.contains('Pendant'))), 'google_product_type'] = 'Pendant'\n", "\n", "# DIRECTIONALS\n", "df.loc[(df['categories'].str.contains('Track')) | \n", "       ((df['manufacture_category'].str.contains('Directional')) & \n", "        (df['categories'].str.contains('Utility'))), 'google_product_type'] = 'Track Light'\n", "\n", "df.loc[(df['manufacture_category'].str.contains('Directional')) & \n", "       (df['categories'].str.contains('Sconces')), 'google_product_type'] = 'Wall Light'\n", "\n", "df.loc[(df['manufacture_category'].str.contains('Direction')) & \n", "       (df['categories'].str.contains('Recess')), 'google_product_type'] = 'Recessed'\n", "\n", "# HALL & FOYER\n", "df.loc[(df['manufacture_category'].str.contains('Foyer')) & \n", "       ((df['ProductName'].str.contains('Pendant')) | \n", "        (df['ProductName'].str.contains('Foyer'))) & \n", "       (df['google_product_type'] == ''), 'google_product_type'] = 'Pendant'\n", "\n", "df.loc[((df['categories'].str.contains('Foyer')) | \n", "        (df['categories'].str.contains('Hall'))) & \n", "       (df['google_product_type'] == ''), 'google_product_type'] = 'Pendant'\n", "\n", "df.loc[(df['manufacture_category'].str.contains('Foyer')) & \n", "       (df['categories'].str.contains('Exterior')) & \n", "       (df['ProductName'].str.contains('Hanging')), 'google_product_type'] = 'Outdoor Hanging Light'\n", "\n", "# LAMPS\n", "df.loc[(df['categories'].str.contains('Lamp')) & \n", "       ((df['manufacture_category'].str.contains('Table')) | \n", "        (df['producttype'].str.contains('Table'))), 'google_product_type'] = 'Table Lamp'\n", "\n", "df.loc[(df['google_product_type'] == '') & \n", "       ((df['manufacture_category'].str.contains('Table')) | \n", "        (df['categories'].str.contains('Table')) | \n", "        (df['ProductName'].str.contains('Table')) | \n", "        (df['producttype'].str.contains('Table'))), 'google_product_type'] = 'Table Lamp'\n", "\n", "df.loc[df['manufacture_category'].str.contains('Buffet'), 'google_product_type'] = 'Table Lamp'\n", "df.loc[df['manufacture_category'].str.contains('Floor'), 'google_product_type'] = 'Floor Lamp'\n", "df.loc[(df['manufacture_category'].str.contains('Desk')) | \n", "       (df['manufacture_category'].str.contains('Piano')), 'google_product_type'] = 'Table Lamp'\n", "\n", "# LANDSCAPE\n", "df.loc[df['categories'].str.contains('Landscape'), 'google_product_type'] = 'Landscape Lighting'\n", "df.loc[(df['producttype'].str.contains('Path')) | \n", "       (df['manufacture_category'].str.contains('Path')), 'google_product_type'] = 'Outdoor Path Light'\n", "df.loc[df['manufacture_category'].str.contains('Bulkhead'), 'google_product_type'] = 'Outdoor Path Light'\n", "df.loc[(df['manufacture_category'].str.contains('Land')) & \n", "       (df['ProductName'].str.contains('Boll')), 'google_product_type'] = 'Outdoor Path Light'\n", "df.loc[(df['manufacture_category'].str.contains('Land')) & \n", "       ((df['ProductName'].str.contains('String')) | \n", "        (df['ProductName'].str.contains('Rock')) | \n", "        (df['ProductName'].str.contains('Spot'))), 'google_product_type'] = 'Landscape Lighting'\n", "\n", "# OUTDOOR\n", "df.loc[(df['manufacture_category'].str.contains('Outdoor')) & \n", "       (df['categories'].str.contains('Exterior')) & \n", "       (df['ProductName'].str.contains('Wall')) & \n", "       (df['google_product_type'] == ''), 'google_product_type'] = 'Outdoor Wall Light'\n", "\n", "df.loc[(df['manufacture_category'].str.contains('Outdoor')) & \n", "       ((df['categories'].str.contains('Utility')) | \n", "        (df['categories'].str.contains('Sconce')) | \n", "        (df['categories'].str.contains('Exterior'))) & \n", "       ((df['ProductName'].str.contains('Wall')) | \n", "        (df['producttype'].str.contains('Sconce'))) & \n", "       (df['google_product_type'] == ''), 'google_product_type'] = 'Outdoor Wall Light'\n", "\n", "df.loc[(df['manufacture_category'].str.contains('Outdoor')) & \n", "       (df['producttype'].str.contains('Wall')), 'google_product_type'] = 'Outdoor Wall Light'\n", "\n", "df.loc[(df['google_product_type'] == '') & \n", "       (df['ProductName'].str.contains('Outdoor Wall')), 'google_product_type'] = 'Outdoor Wall Light'\n", "\n", "df.loc[(df['manufacture_category'].str.contains('Flush')) & \n", "       (df['categories'].str.contains('Exterior')), 'google_product_type'] = 'Outdoor Ceiling Light'\n", "\n", "df.loc[((df['ProductName'].str.contains('Outdoor')) | \n", "        (df['manufacture_category'].str.contains('Outdoor')) | \n", "        (df['categories'].str.contains('Exterior'))) & \n", "       ((df['ProductName'].str.contains('Pendant')) | \n", "        (df['categories'].str.contains('Pendant')) | \n", "        (df['categories'].str.contains('Hanging')) | \n", "        (df['ProductName'].str.contains('Hanging'))), 'google_product_type'] = 'Outdoor Hanging Light'\n", "\n", "df.loc[(df['manufacture_category'].str.contains('Outdoor')) & \n", "       (df['producttype'].str.contains('Post/Pier')), 'google_product_type'] = 'Outdoor Post/Pier Light'\n", "\n", "df.loc[(df['ProductName'].str.contains('Post Lantern')), 'google_product_type'] = 'Outdoor Post/Pier Light'\n", "\n", "df.loc[(df['ProductName'].str.contains('Flood')) & \n", "       ((df['google_product_type'] == 'Outdoor Wall Light') | \n", "        (df['google_product_type'] == '')), 'google_product_type'] = 'Flood Light'\n", "\n", "df.loc[(df['ProductName'].str.contains('Flood')) & \n", "       (df['manufacture_category'].str.contains('Land')), 'google_product_type'] = 'Flood Light'\n", "\n", "df.loc[(df['ProductName'].str.contains('Outdoor Post')) & \n", "       (df['producttype'].str.contains('Posts')) & \n", "       (df['google_product_type'] == ''), 'google_product_type'] = 'Accessory'\n", "\n", "df.loc[(df['ProductName'].str.contains('Photo')) & \n", "       (df['producttype'].str.contains('Sensor')) & \n", "       (df['google_product_type'] == ''), 'google_product_type'] = 'Accessory'\n", "\n", "df.loc[(df['ProductName'].str.contains('Driver')) & \n", "       (df['manufacture_category'].str.contains('Land')) & \n", "       (df['google_product_type'] == ''), 'google_product_type'] = 'Accessory'\n", "\n", "df.loc[((df['manufacture_category'].str.contains('Outoor')) | \n", "        (df['categories'].str.contains('Exterior'))) & \n", "       (df['ProductName'].str.contains('Chandelier')), 'google_product_type'] = 'Outdoor Chandelier'\n", "\n", "df.loc[(df['producttype'].str.contains('Part')), 'google_product_type'] = 'Accessory'\n", "\n", "df = df[~df['manufacture_category'].str.contains('Address')]\n", "\n", "df.loc[((df['ProductName'].str.contains('Post')) | \n", "        (df['producttype'].str.contains('Post'))), 'google_product_type'] = 'Outdoor Post/Pier Light'\n", "\n", "df.loc[(df['manufacture_category'] == 'Wall Lantern') & \n", "       (df['ProductName'].str.contains('Wall Lantern')), 'google_product_type'] = 'Outdoor Wall Light'\n", "\n", "# PENDANTS\n", "df.loc[(df['manufacture_category'].str.contains('Pendant')) & \n", "       (df['google_product_type'] == ''), 'google_product_type'] = 'Pendant'\n", "\n", "df.loc[(df['google_product_type'] == 'Pendant') & \n", "       (df['categories'].str.contains('Mini')), 'google_product_type'] = 'Mini Pendant'\n", "\n", "df.loc[(df['google_product_type'] == '') & \n", "       (df['categories'].str.contains('Pend')), 'google_product_type'] = 'Pendant'\n", "\n", "df.loc[(df['google_product_type'] == '') & \n", "       (df['categories'].str.contains('Mini')), 'google_product_type'] = 'Mini Pendant'\n", "\n", "df[\"bulbs\"] = pd.to_numeric(df['bulbs'], errors = 'coerce')\n", "df.loc[(df['google_product_type'] == 'Mini Pendant') & \n", "       (df['bulbs'] > 4), 'google_product_type'] = 'Pendant'\n", "\n", "df.loc[(df['manufacture_category'].str.contains('Dual Mount')) & \n", "       ((df['categories'].str.contains('Semi')) | \n", "        (df['categories'].str.contains('Pendant'))), 'google_product_type'] = 'Pendant'\n", "\n", "# PICTURE\n", "df.loc[(df['manufacture_category'].str.contains('Picture')), 'google_product_type'] = 'Picture Light'\n", "\n", "# RECESSED\n", "df.loc[((df['manufacture_category'].str.contains('Recess')) | \n", "        (df['categories'].str.contains('Recess'))), 'google_product_type'] = 'Recessed'\n", "\n", "# STEP LIGHTS\n", "df.loc[((df['manufacture_category'].str.contains('Step')) | \n", "        (df['ProductName'].str.contains('Step')) | \n", "        (df['producttype'].str.contains('Step'))), 'google_product_type'] = 'Outdoor Step Light'\n", "\n", "df.loc[(df['google_product_type'].str.contains('Step')) & \n", "       (df['producttype'].str.contains('Indoor')), 'google_product_type'] = 'Wall Light'\n", "\n", "# UNDER CABINET\n", "df.loc[((df['manufacture_category'].str.contains('Undercabinet')) | \n", "        (df['categories'].str.contains('Undercabinet')) | \n", "        (df['ProductName'].str.contains('Undercabinet')) | \n", "        (df['producttype'].str.contains('Undercabinet'))), 'google_product_type'] = 'Under Cabinet'\n", "\n", "# WALL LIGHTS\n", "df.loc[((df['manufacture_category'].str.contains('Wall Bracket')) | \n", "        (df['manufacture_category'].str.contains('Wall Sconce')) | \n", "        (df['manufacture_category'].str.contains('Sconce')) & \n", "        (df['categories'].str.contains('Sconce')) | \n", "        (df['manufacture_category'] == 'Wall Light') | \n", "        (df['manufacture_category'] == 'Wall Lights')), 'google_product_type'] = 'Wall Light'\n", "\n", "df.loc[(df['producttype'].str.contains('Swing Arm')), 'google_product_type'] = 'Wall Lamp'\n", "\n", "df.loc[((df['manufacture_category'].str.contains('Wall Bracket')) | \n", "        (df['manufacture_category'].str.contains('Wall Sconce')) | \n", "        (df['manufacture_category'].str.contains('Sconce')) & \n", "        (df['categories'].str.contains('Exterior'))), 'google_product_type'] = 'Outdoor Wall Light'\n", "\n", "df.loc[((df['manufacture_category'].str.contains('Wall Bracket')) | \n", "        (df['manufacture_category'].str.contains('Sconce')) & \n", "        (df['categories'].str.contains('Exterior'))), 'google_product_type'] = 'Outdoor Wall Light'\n", "\n", "df.loc[(df['ProductName'].str.contains('Wall Sconce')) & \n", "       (df['google_product_type'] == ''), 'google_product_type'] = 'Wall Light'\n", "\n", "df.loc[(df['google_product_type'] == 'Outdoor Ceiling Light') & \n", "       (df['ProductName'].str.contains('Sconce')), 'google_product_type'] = 'Wall Light'\n", "\n", "df = df[~((df['google_product_type'] == 'Accessory') & \n", "          ((df['manufacture_category'].str.contains('Chain')) | \n", "           (df['manufacture_category'].str.contains('Address')) | \n", "           (df['ProductName'].str.contains('Address'))))]\n", "\n", "df.loc[df['google_product_type'].str.contains('Fan'), 'blade_span'] = df['width']"]}, {"cell_type": "markdown", "metadata": {}, "source": ["CATEGORIES (FOR LO) USE FOR PL TOO"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["df[\"categories\"] = \"\"\t\n", "df['number_of_bulbs'] = pd.to_numeric(df['number_of_bulbs'], errors='coerce')\n", "df['blade_span'] = pd.to_numeric(df['blade_span'], errors='coerce')\n", "\n", "# BATH LIGHTS\n", "df.loc[df['google_product_type'] == 'Bathroom Vanity Light', 'categories'] = 'All Products/Bathroom Lighting,All Products/Bathroom Lighting/Bathroom Vanity Lighting'\n", "df.loc[(df['google_product_type'] == 'Flush Mount Ceiling Light') & \n", "       (df['manufacture_category'].str.contains('Bath')), 'categories'] = 'All Products/Bathroom Lighting,All Products/Bathroom Lighting/Bathroom Ceiling Lights'\n", "\n", "# CEILING FAN ACCESSORIES\n", "df.loc[df['google_product_type'] == 'Ceiling Fan Control', 'categories'] = 'All Products/Lighting Accessories,All Products/Lighting Accessories/Ceiling Fan Controls'\n", "df.loc[df['google_product_type'] == 'Ceiling Fan Light Kit', 'categories'] = 'All Products/Lighting Accessories,All Products/Lighting Accessories/Ceiling Fan Light Kits'\n", "\n", "# CEILING FANS\n", "df.loc[(df['google_product_type'].str.contains('Fan')) & \n", "       (df['categories'] == ''), 'categories'] = 'All Products/Ceiling Fans'\n", "df.loc[df['google_product_type'].str.contains('Outdoor Ceiling Fan'), 'categories'] = df['categories'] + ',All Products/Ceiling Fans/Outdoor Ceiling Fans'\n", "df.loc[(df['google_product_type'].str.contains('Ceiling Fan')) & \n", "       (df['w_d_rated'].str.contains('Dry')), 'categories'] = df['categories'] + ',All Products/Ceiling Fans/Indoor Ceiling Fans'\n", "df.loc[df['google_product_type'].str.contains('Wall Fan'), 'categories'] = df['categories'] + ',All Products/Ceiling Fans/Wall Fans'\n", "df.loc[(df['google_product_type'].str.contains('Fan')) & \n", "       (df['type_of_bulbs'].str.contains('LED')), 'categories'] = df['categories'] + ',All Products/Ceiling Fans/LED Ceiling Fans'\n", "df.loc[(df['google_product_type'].str.contains('Fan')) & \n", "       (df['blade_span'] > 60), 'categories'] = df['categories'] + ',All Products/Ceiling Fans/Large Ceiling Fans'\n", "df.loc[df['google_product_type'].str.contains('Hugger'), 'categories'] = df['categories'] + ',All Products/Ceiling Fans/Flush Mount Ceiling Fans'\n", "df.loc[(df['google_product_type'].str.contains('Ceiling Fan')) & \n", "       (df['number_of_bulbs'] > 0), 'categories'] = df['categories'] + ',All Products/Ceiling Fans/Ceiling Fans with Lights'\n", "\n", "# CEILING LIGHTS\n", "df.loc[(df['google_product_type'].str.contains('Ceiling Light')) & \n", "       ((df['manufacture_category'].str.contains('Close')) | \n", "        (df['manufacture_category'].str.contains('Mount'))) & \n", "       (df['categories'] == ''), 'categories'] = 'All Products/Ceiling Lights'\n", "df.loc[(df['google_product_type'].str.contains('Flush Mount')) & \n", "       (df['manufacture_category'].str.contains('Close-')), 'categories'] = df['categories'] + ',All Products/Ceiling Lights/Flush Mount Ceiling Lights'\n", "df.loc[(df['google_product_type'].str.contains('Flush Mount')) & \n", "       (df['manufacture_category'].str.contains('Surface')), 'categories'] = df['categories'] + ',All Products/Ceiling Lights/Flush Mount Ceiling Lights'\n", "df.loc[(df['google_product_type'].str.contains('Flush Mount')) & \n", "       (df['manufacture_category'].str.contains('Outdoor')) & \n", "       (df['categories'] == ''), 'categories'] = 'All Products/Outdoor Lights,All Products/Outdoor Lights/Outdoor Ceiling Lights'\n", "df.loc[df['google_product_type'].str.contains('Semi'), 'categories'] = 'All Products/Ceiling Lights,All Products/Ceiling Lights/Semi-Flush Ceiling Lights'\n", "df.loc[df['google_product_type'].str.contains('Track'), 'categories'] = 'All Products/Ceiling Lights,All Products/Ceiling Lights/Track Lighting'\n", "df.loc[((df['ProductName'].str.contains('LED')) | \n", "        (df['type_of_bulbs'].str.contains('LED'))) & \n", "       (df['google_product_type'].str.contains('Ceiling Light')), 'categories'] = df['categories'] + ',All Products/Ceiling Lights/LED Ceiling Lights'\n", "df.loc[(df['categories'] == '') & \n", "       (df['google_product_type'] == 'Flush Mount Ceiling Light'), 'categories'] = 'All Products/Ceiling Lights,All Products/Ceiling Lights/Flush Mount Ceiling Lights'\n", "df.loc[df['categories'] == ',All Products/Ceiling Lights/LED Ceiling Lights', 'categories'] = 'All Products/Ceiling Lights,All Products/Ceiling Lights/Flush Mount Ceiling Lights/All Products/Ceiling Lights/LED Ceiling Lights'\n", "df.loc[df['categories'] == 'All Products/Ceiling Lights', 'categories'] = 'All Products/Ceiling Lights,All Products/Ceiling Lights/Flush Mount Ceiling Lights'\n", "df.loc[df['ProductName'].str.contains('Light Kit or Chandelier'), 'categories'] = 'All Products/Ceiling Lights,All Products/Ceiling Lights/Semi-Flush Ceiling Lights,All Products/Lighting Accessories,All Products/Lighting Accessories/Ceiling Fan Light Kits'\n", "\n", "# CHANDELIERS\n", "df.loc[(df['google_product_type'].str.contains('Chandelier')) & \n", "       (df['categories'] == ''), 'categories'] = 'All Products/Chandeliers & Pendants'\n", "df.loc[(df['google_product_type'].str.contains('Chandelier')) & \n", "       (df['categories'].str.contains('Chandeliers & Pendants')), 'categories'] = df['categories'] + ',All Products/Chandeliers & Pendants/Chandeliers'\n", "df.loc[(df['google_product_type'].str.contains('Chandelier')) & \n", "       (df['style'].str.contains('Traditional')), 'categories'] = df['categories'] + ',All Products/Chandeliers & Pendants/Traditional Chandeliers'\n", "df.loc[(df['google_product_type'].str.contains('Chandelier')) & \n", "       (df['style'].str.contains('Transitional')), 'categories'] = df['categories'] + ',All Products/Chandeliers & Pendants/Transitional Chandeliers'\n", "df.loc[(df['google_product_type'].str.contains('Chandelier')) & \n", "       (df['style'].str.contains('Rustic')), 'categories'] = df['categories'] + ',All Products/Chandeliers & Pendants/Rustic Chandeliers'\n", "df.loc[(df['google_product_type'].str.contains('Chandelier')) & \n", "       ((df['style'].str.contains('Contemporary')) | \n", "        (df['style'].str.contains('Modern'))), 'categories'] = df['categories'] + ',All Products/Chandeliers & Pendants/Contemporary Chandeliers'\n", "df.loc[(df['google_product_type'].str.contains('Chandelier')) & \n", "       (df['style'].str.contains('Transitional')), 'categories'] = df['categories'] + ',All Products/Chandeliers & Pendants/Transitional Chandeliers'\n", "df.loc[(df['google_product_type'].str.contains('Chandelier')) & \n", "       (df['google_product_type'].str.contains('Mini')), 'categories'] = df['categories'] + ',All Products/Chandeliers & Pendants/Mini Chandeliers'\n", "df.loc[df['ProductName'].str.contains('Light Kit or Semi Flush'), 'categories'] = 'All Products/Chandeliers & Pendants,All Products/Chandeliers & Pendants/Chandeliers,All Products/Lighting Accessories,All Products/Lighting Accessories/Ceiling Fan Light Kits'\n", "\n", "# FANDLIERS\n", "df.loc[(df['google_product_type'].str.contains('Fandelier')) & (df['categories'] == ''), 'categories'] = 'All Products/Ceiling Fans,All Products/Ceiling Fans/Ceiling Fan Chandeliers'\n", "\n", "# FLOOD LIGHTS\n", "df.loc[(df['google_product_type'].str.contains('Flood')) & (df['categories'] == ''), 'categories'] = 'All Products/Outdoor Lights,All Products/Outdoor Lights/Outdoor Wall Lights,All Products/Outdoor Lights/Outdoor Wall Lights/Flood Lights'\n", "\n", "# HOME DECOR\n", "df.loc[df['google_product_type'].str.contains('Door Chime'), 'categories'] = 'All Products/Home Decor,All Products/Home Decor/Door Chimes'\n", "\n", "# LANDSCAPE\n", "df.loc[df['google_product_type'].str.contains('Landscape'), 'categories'] = 'All Products/Outdoor Lights/Landscape Lighting'\n", "df.loc[(df['categories'].str.contains('Landscape Lighting')) & (df['google_product_type'].str.contains('Outdoor Path')), 'categories'] += ',All Products/Outdoor Lights/Landscape Lighting/Pathway Lights'\n", "df.loc[(df['categories'].str.contains('Landscape Lighting')) & ((df['google_product_type'].str.contains('Outdoor Step')) | (df['ProductName'].str.contains('Deck'))), 'categories'] += ',All Products/Outdoor Lights/Landscape Lighting/Deck & Step Lights'\n", "df.loc[(df['google_product_type'].str.contains('Path')) & (df['manufacture_category'].str.contains('Landscape')), 'categories'] = 'All Products/Outdoor Lights/Landscape Lighting,All Products/Outdoor Lights/Landscape Lighting/Pathway Lights'\n", "df.loc[df['google_product_type'].str.contains('Step'), 'categories'] = 'All Products/Outdoor Lights/Landscape Lighting,All Products/Outdoor Lights/Landscape Lighting/Deck & Step Lights'\n", "df.loc[df['google_product_type'] == 'Outdoor Path Light', 'categories'] = 'All Products/Outdoor Lights,All Products/Outdoor Lights/Landscape Lighting,All Products/Outdoor Lights/Landscape Lighting/Pathway Lights'\n", "\n", "# LAMPS\n", "df.loc[df['google_product_type'].str.contains('Lamp'), 'categories'] = 'All Products/Lamps'\n", "df.loc[df['google_product_type'].str.contains('Table Lamp'), 'categories'] += ',All Products/Lamps/Table Lamps'\n", "df.loc[df['google_product_type'].str.contains('Floor Lamp'), 'categories'] += ',All Products/Lamps/Floor Lamps'\n", "df.loc[df['producttype'].str.contains('Accent Lamp'), 'categories'] += ',All Products/Lamps/Accent Lamps'\n", "\n", "# OUTDOOR LIGHTS\n", "df.loc[df['google_product_type'] == 'Outdoor Ceiling Light', 'categories'] = ''\n", "df.loc[(df['google_product_type'].str.contains('Outdoor')) & (df['categories'] == ''), 'categories'] = 'All Products/Outdoor Lights'\n", "df.loc[df['google_product_type'].str.contains('Outdoor Ceiling'), 'categories'] += ',All Products/Outdoor Lights/Outdoor Ceiling Lights'\n", "df.loc[df['google_product_type'].str.contains('Outdoor Hanging'), 'categories'] += ',All Products/Outdoor Lights/Outdoor Hanging Lights'\n", "df.loc[df['google_product_type'].str.contains('Outdoor Post'), 'categories'] += ',All Products/Outdoor Lights/Outdoor Post/Pier Lights'\n", "df.loc[df['google_product_type'].str.contains('Outdoor Wall'), 'categories'] += ',All Products/Outdoor Lights/Outdoor Wall Lights'\n", "\n", "# PENDANTS\n", "df.loc[(df['google_product_type'].str.contains('Pendant')) & (df['categories'] == ''), 'categories'] = 'All Products/Chandeliers & Pendants,All Products/Chandeliers & Pendants/Pendants'\n", "df.loc[df['google_product_type'].str.contains('Mini Pendant'), 'categories'] += ',All Products/Chandeliers & Pendants/Pendant Lights/Mini Pendants'\n", "df.loc[(df['google_product_type'].str.contains('Pendant')) & (df['producttype'].str.contains('Bowl')), 'categories'] += ',All Products/Chandeliers & Pendants/Pendant Lights/Bowl Pendants'\n", "df.loc[(df['google_product_type'].str.contains('Pendant')) & (df['producttype'].str.contains('Bell')), 'categories'] += ',All Products/Chandeliers & Pendants/Pendant Lights/Bell Pendants'\n", "df.loc[(df['google_product_type'].str.contains('Pendant')) & (df['producttype'].str.contains('Drum')), 'categories'] += ',All Products/Chandeliers & Pendants/Pendant Lights/Drum Pendants'\n", "df.loc[df['google_product_type'].str.contains('Linear'), 'categories'] += ',All Products/Chandeliers & Pendants/Linear Chandeliers & Pendants'\n", "df.loc[(df['google_product_type'].str.contains('Linear')) & ((df['manufacture_category'].str.contains('Pendant')) | (df['producttype'].str.contains('Pendant')) | (df['baseCat'].str.contains('Pendant'))), 'categories'] += ',All Products/Chandeliers & Pendants/Linear Chandeliers & Pendants/Linear Pendants'\n", "df.loc[(df['manufacture_category'].str.contains('Foyer')) | (df['ProductName'].str.contains('Foyer')), 'categories'] += ',All Products/Chandeliers & Pendants/Foyer Lights'\n", "\n", "# RECESSED\n", "df.loc[df['google_product_type'].str.contains('Accent'), 'categories'] = 'All Products/Lamps/Accent Lamps'\n", "\n", "# RECESSED\n", "df.loc[df['google_product_type'].str.contains('Recess'), 'categories'] = 'All Products/Ceiling Lights,All Products/Ceiling Lights/Recessed Lighting'\n", "\n", "# UNDER CABINET\n", "df.loc[(df['google_product_type'].str.contains('Under Cabinet')) | (df['manufacture_category'].str.contains('Under')), 'categories'] = 'All Products/Wall Lights,All Products/Wall Lights/Under Cabinet Lights'\n", "\n", "# WALL LIGHTS\n", "df.loc[(df['google_product_type'].str.contains('Wall')) & (df['categories'] == ''), 'categories'] = 'All Products/Wall Lights'\n", "df.loc[df['google_product_type'].str.contains('Wall Lamp'), 'categories'] += ',All Products/Wall Lights/Wall Lamps'\n", "df.loc[(df['google_product_type'].str.contains('Wall Light')) & ((df['manufacture_category'].str.contains('Sconce')) | (df['manufacture_category'].str.contains('Bracket'))), 'categories'] += ',All Products/Wall Lights/Wall Sconces'\n", "df.loc[(df['google_product_type'].str.contains('Wall Light')) & (df['manufacture_category'].str.contains('Step')), 'categories'] += ',All Products/Wall Lights/Step Lights'\n", "df.loc[(df['categories'].str.contains('Wall Light')) & (df['google_product_type'].str.contains('Picture Light')), 'categories'] += ',All Products/Wall Lights/Picture Lights'\n", "df.loc[(df['categories'] == '') & (df['google_product_type'].str.contains('Picture')), 'categories'] = 'All Products/Wall Lights/Picture Lights'\n", "df.loc[(df['categories'].str.contains('Wall Light')) & ((df['ProductName'].str.contains('LED')) | (df['type_of_bulbs'].str.contains('LED'))), 'categories'] += ',All Products/Wall Lights/LED Wall Lights'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["STYLE"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["df[\"style_filter\"] = \"\"\n", "\n", "style_filters = {\n", "    \"Coastal\": [\"Coastal\"],\n", "    \"Hollywood Regency\": [\"Regency\"],\n", "    \"French Country\": [\"French\"],\n", "    \"Industrial\": [\"Industrial\"],\n", "    \"Midcentury Modern\": [\"Midcentury\", \"Mid-century\", \"Mid-Century\"],\n", "    \"Craftsman\": [\"Craftsman\"],\n", "    \"Modern Farmhouse\": [\"Farmhouse\"],\n", "    \"Rustic\": [\"Rustic\", \"Colonial\", \"Lodge\"],\n", "    \"Tiffany\": [\"<PERSON>\"],\n", "    \"Traditional\": [\"Traditional\"],\n", "    \"Art Deco\": [\"Art Deco\", \"Geometric\"],\n", "    \"Asian Zen\": [\"Asian\"],\n", "    \"Bohemian & Global\": [\"Bohemian\"],\n", "    \"Eclectic\": [\"Eclectic\", \"Whim\"],\n", "    \"Minimalist\": [\"Minimal\"],\n", "    \"New American Modern\": [\"New American\"],\n", "    \"Shabby Chic\": [\"Chic\"],\n", "    \"Urban Modern\": [\"Urban\"],\n", "    \"Victorian\": [\"Victorian\", \"Gothic\", \"Old World\"],\n", "    \"Vintage\": [\"Vintage\", \"Restoration\", \"Period\"],\n", "    \"Modern\": [\"Modern\", \"Metro\"],\n", "    \"Contemporary\": [\"Contemporary\"],\n", "    \"Transitional\": [\"Transitional\", \"Regional\"]\n", "}\n", "\n", "def update_style_filter(row):\n", "    for style_filter, keywords in style_filters.items():\n", "        if any(keyword.lower() in row['style'].lower() for keyword in keywords):\n", "            return style_filter\n", "    return \"Transitional\"\n", "\n", "df['style_filter'] = df.apply(update_style_filter, axis=1)\n", "\n", "df['vendor_style'] = df['style'].apply(lambda x: x.replace(\", \", \"\") if x.startswith(\",\") else x)\n", "df['style'] = df['style'].apply(lambda x: x.replace(\", \", \"\") if x.startswith(\",\") else x)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["NAME ATTRIBUTES"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["df['ProductName'] = df['ProductName'].str.replace('One Light', '')\n", "df['ProductName'] = df['ProductName'].str.replace('Two Light', '')\n", "df['ProductName'] = df['ProductName'].str.replace('Three Light', '')\n", "df['ProductName'] = df['ProductName'].str.replace('Four Light', '')\n", "df['ProductName'] = df['ProductName'].str.replace('Five Light', '')\n", "df['ProductName'] = df['ProductName'].str.replace('Six Light', '')\n", "df['ProductName'] = df['ProductName'].str.replace('Seven Light', '')\n", "df['ProductName'] = df['ProductName'].str.replace('Eight Light', '')\n", "df['ProductName'] = df['ProductName'].str.replace('Nine Light', '')\n", "df['ProductName'] = df['ProductName'].str.replace('Ten Light', '')\n", "df['ProductName'] = df['ProductName'].str.replace('Eleven Light', '')\n", "df['ProductName'] = df['ProductName'].str.replace('Twelve Light', '')\n", "df['ProductName'] = df['ProductName'].str.replace('Thirteen Light', '')\n", "df['ProductName'] = df['ProductName'].str.replace('Fourteen Light', '')\n", "df['ProductName'] = df['ProductName'].str.replace('Fifteen Light', '')\n", "df['ProductName'] = df['ProductName'].str.replace('Sixteen Light', '')\n", "df['ProductName'] = df['ProductName'].str.replace('Seventeen Light', '')\n", "df['ProductName'] = df['ProductName'].str.replace('Eighteen Light', '')\n", "df['ProductName'] = df['ProductName'].str.replace('Nineteen Light', '')\n", "df['ProductName'] = df['ProductName'].str.replace('Twenty Light', '')\n", "df['ProductName'] = df['ProductName'].str.replace('Twenty One Light', '')\n", "df['ProductName'] = df['ProductName'].str.replace('Twenty Two Light', '')\n", "df['ProductName'] = df['ProductName'].str.replace('Twenty Three Light', '')\n", "df['ProductName'] = df['ProductName'].str.replace('Twenty Four Light', '')\n", "\n", "df['ProductName'] = df['ProductName'].apply(lambda x: re.sub(r'\\b(1[0-9]|2[0-4]|[1-9]) Light\\b', '', x, flags=re.IGNORECASE))\n", "df['ProductName'] = df['ProductName'].apply(lambda x: re.sub(r'\\b(1[0-9]|2[0-4]|[1-9])\\b', '', x))\n", "\n", "df['ProductName'] = df['ProductName'].str.replace('Bath', 'Bathroom Vanity Light')\n", "df['ProductName'] = df['ProductName'].str.replace('Vanity', 'Bathroom Vanity Light')\n", "df['ProductName'] = df['ProductName'].str.replace('Bath/', 'Bath')\n", "df['ProductName'] = df['ProductName'].str.replace('Bath Bathroom', 'Bathroom')\n", "df['ProductName'] = df['ProductName'].str.replace('BathBathroom', 'Bathroom')\n", "df['ProductName'] = df['ProductName'].str.replace('Light Light', 'Light')\n", "df['ProductName'] = df['ProductName'].str.replace('Vanity Light Bar', 'Vanity Light')\n", "\n", "df['name'] = df['name'].str.replace(\"  \", \" \")\n", "df['name'] = df['name'].str.replace(\"``'\", '\"')\n", "df['name'] = df['name'].str.replace(\"''\", '\"')\n", "df['name'] = df['name'].str.replace(\"`\", \"\")\n", "df['name'] = df['name'].str.replace(\"_\", \" \")\n", "\n", "df['name2'] = df['name2'].str.replace(\"  \", \" \")\n", "df['name2'] = df['name2'].str.replace(\"``'\", '\"')\n", "df['name2'] = df['name2'].str.replace(\"''\", '\"')\n", "df['name2'] = df['name2'].str.replace(\"`\", \"\")\n", "df['name2'] = df['name2'].str.replace(\"_\", \" \")\n", "\n", "# create a list of strings to replace\n", "lights = [f\"{i} Light \" for i in range(1, 26)] + [\"No Family \", \"One Light \", \"Two Light \", \"Three Light \", \"Four Light \", \n", "         \"Five Light \", \"Six Light \", \"Seven Light \", \"Eight Light \", \"Nine Light \", \"Ten Light \", \"Eleven Light \", \n", "         \"Twelve Light \", \"Thirteen Light \", \"Fourteen Light \", \"Fifteen Light \", \"Sixteen Light \"]\n", "\n", "# replace all occurrences of these strings\n", "for light in lights:\n", "    df['name2'] = df['name2'].str.replace(light, \"\")\n", "\n", "df['name2'] = df['name2'].str.replace('\"', '\" ')\n", "df['name2'] = df['name2'].str.replace(\"  \", \" \")\n", "df['name2'] = df['name2'].str.replace(\"Led\", \"LED\")\n", "df['name2'] = df['name2'].str.replace(\"Bath\", \"Bathroom Vanity Light\")\n", "df['name2'] = df['name2'].str.replace(\"Semi Flush\", \"Semi-Flush\")\n", "df['name2'] = df['name2'].str.replace(\"Vanity Light/ Vanity Light\", \"Vanity Light\")\n", "df['name2'] = df['name2'].str.replace(\"Vanity Light Vanity\", \"Vanity Light\")\n", "df['name2'] = df['name2'].str.replace(\"-Light Vanity\", \"-Light Bathroom Vanity\")\n", "df['name2'] = df['name2'].str.replace(\"LED Vanity\", \"LED Bathroom Vanity\")\n", "df['name2'] = df['name2'].str.replace(\"Light/Vanity\", \"Light\")\n", "df['name2'] = df['name2'].str.replace(\"Flushmount\", \"Flush Mount\")\n", "df['name2'] = df['name2'].str.replace(\"Semi Flush Mount\", \"\")\n", "df['name2'] = df['name2'].str.replace(\"  \", \" \")\n", "df['name2'] = df['name2'].str.replace(\"Bathroom Vanity in\", \"Bathroom Vanity Light in\")\n", "df['name2'] = df['name2'].str.replace(\"Cct\", \"CCT\")\n", "df['name2'] = df['name2'].str.replace(\"Dcp-Hub\", \"DCP-Hub\")\n", "df['name2'] = df['name2'].str.replace(\" With \", \" with \")\n", "df['name2'] = df['name2'].str.replace(\" And \", \" and \")\n", "df['name2'] = df['name2'].str.replace(\" On Metal\", \"\")\n", "df['name2'] = df['name2'].str.replace(\" with Clear Glass\", \"\")\n", "df['name2'] = df['name2'].str.replace(\" with Clear Seedy Glass\", \"\")\n", "df['name2'] = df['name2'].str.replace(\" with Half Opal Glass\", \"\")\n", "df['name2'] = df['name2'].str.replace(\" with True Opal Glass\", \"\")\n", "df['name2'] = df['name2'].str.replace(\" and True Opal Glass\", \"\")\n", "df['name2'] = df['name2'].str.replace(\" with Ribbed Opal Glass\", \"\")\n", "df['name2'] = df['name2'].str.replace(\" with Opal Glass\", \"\")\n", "df['name2'] = df['name2'].str.replace(\" and Opal Glass\", \"\")\n", "df['name2'] = df['name2'].str.replace(\" with Honeycomb Glass\", \"\")\n", "df['name2'] = df['name2'].str.replace(\" with Ripple Glass Glass\", \"\")\n", "df['name2'] = df['name2'].str.replace(\" with Ripple Glass\", \"\")\n", "df['name2'] = df['name2'].str.replace(\" with Sandblasted Seedy Glass\", \"\")\n", "df['name2'] = df['name2'].str.replace(\" with Crackle Glass\", \"\")\n", "df['name2'] = df['name2'].str.replace(\" with Hammered Clear Glass\", \"\")\n", "df['name2'] = df['name2'].str.replace(\" with Ribbed Half Opal Glass\", \"\")\n", "df['name2'] = df['name2'].str.replace(\" with Ribbed <PERSON> Glass\", \"\")\n", "df['name2'] = df['name2'].str.replace(\" with Vodka Ice Glass\", \"\")\n", "df['name2'] = df['name2'].str.replace(\" with Silk Screened White Glass\", \"\")\n", "df['name2'] = df['name2'].str.replace(\" with Silk Screened Opal Glass\", \"\")\n", "df['name2'] = df['name2'].str.replace(\" with Silk Screen Opal Glass\", \"\")\n", "df['name2'] = df['name2'].str.replace(\" with Artisinal Water Glass\", \"\")\n", "df['name2'] = df['name2'].str.replace(\" with Smoke Glass\", \"\")\n", "df['name2'] = df['name2'].str.replace(\"<PERSON><PERSON>\", \"Nickel\")\n", "\n", "df['name'] = df['name2']\n", "df['name'] = df['name'].str.replace('\" \" ', '\" ')\n", "\n", "df['name'] = df.apply(lambda row: row['name'].replace('Ceiling Fan', row['google_product_type']), axis=1)\n", "\n", "df = df[~df['ProductName'].str.contains('Commercial')]\n", "\n", "df['name'] = df['name'].str.replace(\"/\", \" with \", regex=False)\n", "df['name'] = df.apply(lambda row: row['name'].replace(str(row['number_of_bulbs']) + \" Light\", \"\"), axis=1)\n", "df['name'] = df['name'].str.replace(\"  \", \" \", regex=False).str.replace(\" w \", \" \", regex=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["GLASS UPDATE REVISA LO DE LOS NOMBRES"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["glass_replaces = {\n", "    '-CL-': 'Clear Glass',\n", "    '-SDY-': 'Clear Seedy Glass',\n", "    '-OP-': 'Half Opal Glass',\n", "    '-HNC-': 'Honeycomb Glass',\n", "    '-RPG-': 'Ripple Glass',\n", "    '-SSD-': 'Sandblasted Seedy Glass',\n", "    '-TO-': 'True Opal Glass',\n", "    '-CRK-': 'Crackle Glass',\n", "    '-HMC-': 'Hammered Clear Glass',\n", "    '-RIO-': 'Ribbed Opal Glass',\n", "    '-RIC-': 'Ribbed Clear Glass',\n", "    '-VICE-': 'Vodka Ice Glass',\n", "    '-SSW-': 'Silk Screened White Glass',\n", "    '-SSOP-': 'Silk Screened Opal Glass',\n", "    '-ARW-': 'Artisinal Water Glass',\n", "    '-SM-': 'Smoke Glass',\n", "    '-CRY-': 'Optic Glass Inserts',\n", "    '-AO-': 'Acrylic Opal Glass',\n", "    '-RI-': 'Ribbed Clear Glass',\n", "    '-RICE-': 'Ribbed Ice Glass',\n", "    '-MS-': '<PERSON><PERSON>wirl Glass',\n", "    '-BFA-': 'Brown Faux Alabaster',\n", "    '-GFA-': 'Gray Faux Alabaster',\n", "}\n", "\n", "for key, value in glass_replaces.items():\n", "    df.loc[df['sku'].str.contains(key), 'glass'] = value\n", "\n", "glass2_replaces = {\n", "    'Opal': ['Opal'],\n", "    'Clear': ['Clear'],\n", "    'White': ['White'],\n", "    'Clear Seedy': ['Seedy'],\n", "    'Clear Textured': ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', 'Swirl'],\n", "    'Frosted': ['Ice'],\n", "    '<PERSON>/<PERSON>': ['<PERSON>'],\n", "    'Silver/Gray': ['<PERSON>'],\n", "    'Smoke': ['Smoke'],\n", "}\n", "\n", "for key, values in glass2_replaces.items():\n", "    for value in values:\n", "        df.loc[df['glass'].str.contains(value), 'glass2'] = key"]}, {"cell_type": "markdown", "metadata": {}, "source": ["UPDATE DESCRIPTIONS"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["df['short_description'] = df['short_description'].str.replace(\" \", \" \")\n", "df['short_description'] = df['short_description'].str.replace(\"`\", \"\")\n", "df['short_description'] = df['short_description'].str.replace(\"_\", \" \")\n", "\n", "df.loc[df['collection_name'] == \"No Family\", 'short_description'] = df.loc[df['collection_name'] == \"No Family\", 'short_description'].str.replace(\"No Family\", \"\", 10)\n", "\n", "df['short_description'] = df['short_description'].str.replace(\" \", \" \", 100)\n", "df['short_description'] = df['short_description'].str.replace(\"°\", \"&deg\", 10)\n", "\n", "df['short_description'] = df.apply(lambda row: row['short_description'].replace(row['material'], row['material'].lower(), 10), axis=1)\n", "\n", "df['description'] = df['description'].str.replace(\" \", \" \", 100)\n", "df['description'] = df['description'].str.replace(\"Multi-Light \", \"\", 100)\n", "\n", "df['short_description'] = df['short_description'].str.replace(\"Ceiling Fan\", str(df['google_product_type']), 1)\n", "df['short_description'] = df['short_description'].str.replace(\" | \", \" \")\n", "\n", "df['short_description'] = df['short_description'].str.replace(\"Bath/Vanity\", \"Bathroom Vanity Light\")\n", "df['short_description'] = df['short_description'].str.replace(\"Bath/ Vanity\", \"Bathroom Vanity Light\")\n", "df['short_description'] = df['short_description'].str.replace(\"Bath / Vanity\", \"Bathroom Vanity Light\")\n", "df['short_description'] = df['short_description'].str.replace(\"-Light Vanity Light\", \"-Light Bathroom Vanity Light\")\n", "df = df[df['short_description'] != \"__EMPTY__VALUE__\"]\n", "\n", "df['description'] = df['description'].str.replace(\"Â\", \"\")\n", "df['description'] = df['description'].str.replace(\"\\xa0\", \"\")\n", "\n", "df['description'] = df.apply(lambda row: row['description'].replace(\"__EMPTY__VALUE__\", row['short_description'] + \" by \" + row['brand']), axis=1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["UPDATE SEO ATTRIBUTES"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["df['meta_title'] = df['meta_title'].str.replace(\"  \", \" \", regex=False)\n", "df['meta_title'] = df['meta_title'].str.replace(\"''\", '\"', regex=False)\n", "df['meta_title'] = df['meta_title'].str.replace(\"`\", \"\", regex=False)\n", "df['meta_title'] = df['meta_title'].str.replace(\"_\", \" \", regex=False)\n", "df['meta_title'] = df['name2']\n", "df['meta_title'] = df['meta_title'].str.replace('\" \"', '\" ', regex=False)\n", "df['meta_title'] = df.apply(lambda row: row['meta_title'].replace(\"Ceiling Fan\", row['google_product_type']), axis=1)\n", "df['meta_title'] = df.apply(lambda row: row['meta_title'].replace(str(row['number_of_bulbs']) + \"-Light \" + str(row['number_of_bulbs']) + \" Light\", str(row['number_of_bulbs']) + \"-Light\"), axis=1)\n", "\n", "df['meta_keywords'] = df.apply(lambda row: \",\".join([row['mpn'], str(row['upc']), row['brand'], row['categories'], row['manufacture_category'], row['ProductName'], row['LightsAmericaFinish'], str(row['type_of_bulbs']), row['manufacturer_finish'], row['collection_name'], row['style'], row['w_d_rated'], row['material'], row['warranty']]), axis=1)\n", "df['meta_keywords'] = df['meta_keywords'].str.replace(\",__EMPTY__VALUE__,\", \",\", regex=False)\n", "df['meta_keywords'] = df['meta_keywords'].str.replace(\",,\", \",\", regex=False)\n", "df['meta_keywords'] = df['meta_keywords'].str.replace(\"``\", '\"', regex=False)\n", "df['meta_keywords'] = df['meta_keywords'].str.replace(\"''\", '\"', regex=False)\n", "df['meta_keywords'] = df['meta_keywords'].str.replace('\"', 'in.', regex=False)\n", "\n", "df['meta_description'] = df['meta_description'].str.replace(\"  \", \" \", regex=False)\n", "df['meta_description'] = df['meta_description'].str.replace(\"`\", \"\", regex=False)\n", "df['meta_description'] = df['meta_description'].str.replace(\"_\", \" \", regex=False)\n", "\n", "df['meta_description'] = df.apply(lambda row: \"This \" + row['manufacture_category'] + \" from \" + row['brand'] + \" is part of the \" + row['collection_name'] + \" Collection, has \" + str(row['number_of_bulbs']) + (\" lights\" if str(row['number_of_bulbs']) != \"1\" else \" light\") + \" and comes in a \" + row['finish'] + \" finish.\", axis=1)\n", "df['meta_description'] = df.apply(lambda row: row['meta_description'].replace(\", has \" + str(row['number_of_bulbs']) + \" lights\", \"\") if str(row['number_of_bulbs']) == \"0\" else row['meta_description'], axis=1)\n", "df['meta_description'] = df['meta_description'].str.replace('\" \"', '\" ', regex=False)\n", "df['meta_description'] = df.apply(lambda row: row['meta_description'].replace(\"Ceiling Fan\", row['google_product_type']), axis=1)\n", "df['meta_description'] = df.apply(lambda row: row['meta_description'].replace(str(row['number_of_bulbs']) + \"-Light \" + str(row['number_of_bulbs']) + \" Light\", str(row['number_of_bulbs']) + \"-Light\"), axis=1)\n", "\n", "\n", "# URL KEY FOR PL SITE\n", "df['url_key'] = df['name'] + \"-\" + df['mpn']\n", "df['url_key'] = df['url_key'].str.lower()\n", "df['url_key'] = df['url_key'].str.replace(\" \", \"-\", regex=False)\n", "df['url_key'] = df['url_key'].str.replace('\"', '', regex=False)\n", "df['url_key'] = df['url_key'].str.replace('`', '', regex=False)\n", "df['url_key'] = df['url_key'].str.replace('/', '-', regex=False)\n", "df['url_key'] = df['url_key'].str.replace('no-family', '-', regex=False)\n", "df['url_key'] = df['url_key'].str.replace('--', '-', regex=False)\n", "df['url_key'] = df['url_key'].str.replace(\",\", \"\", regex=False)\n", "df['url_key'] = df['url_key'].str.replace('.', '', regex=False)\n", "df['url_key'] = df['url_key'].str.replace(\":\", \"\", regex=False)\n", "df['url_key'] = df['url_key'].str.replace(\"&\", \"\", regex=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["MORE IMPORTANT UPDATES"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["df['ranking'] = \"7\"\n", "df = df.astype(str)\n", "\n", "# NEW FROM DATE\n", "today = datetime.now()\n", "df['news_from_date'] = today.strftime(\"%m/%d/%Y\")\n", "\n", "today30 = today + <PERSON><PERSON><PERSON>(days=60)\n", "df['news_to_date'] = today30.strftime(\"%m/%d/%Y\")\n", "\n", "# SKU GROUP\n", "df['sku_group'] = df.apply(lambda row: row['sku_group'] + \" \" + row['collection_name'] + \" \" + str(row['number_of_bulbs']) + \"-lt \" + row['google_product_type'], axis=1)\n", "df['sku_group'] = df.apply(lambda row: row['sku_group'] + row['color_temp'] if row['color_temp'] != \"__EMPTY__VALUE__\" else row['sku_group'], axis=1)\n", "df['sku_group'] = df['sku_group'].str.replace(\" \", \"-\", regex=False)\n", "df['sku_group'] = df['sku_group'].str.lower()\n", "\n", "# OTHER\n", "df['material'] = df['material'].str.lower()\n", "df = df.drop(columns = \"glass_filter\")\n", "df = df.rename(columns={'la_id': 'fiftytwo_id', 'glass2': 'glass_filter'})\n", "df['weight'] = df['weight'].astype(str).replace('.', '')\n", "\n", "# REMOVING SOME GOOGLE PRODUCT TYPE\n", "df = df[(~df['google_product_type'].str.contains(\"Landscape\")) & (~df['google_product_type'].str.contains(\"Track \")) & (~df['google_product_type'].str.contains(\"Speciality\")) & (~df['google_product_type'].str.contains(\"Recessed\"))] \n", "\n", "# Convert the DF to string and replace empty values with __EMPTY__VALUE__\n", "df = df.astype(str)\n", "df = df.replace(\"\", '__EMPTY__VALUE__')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["FINAL CLEAN UP"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "sku", "rawType": "object", "type": "string"}, {"name": "mpn", "rawType": "object", "type": "string"}, {"name": "vendor_code", "rawType": "object", "type": "string"}, {"name": "fiftytwo_id", "rawType": "object", "type": "string"}, {"name": "upc", "rawType": "object", "type": "string"}, {"name": "dn", "rawType": "object", "type": "string"}, {"name": "imap", "rawType": "object", "type": "string"}, {"name": "map_enabled", "rawType": "object", "type": "string"}, {"name": "price", "rawType": "object", "type": "string"}, {"name": "msrp", "rawType": "object", "type": "string"}, {"name": "map_price", "rawType": "object", "type": "string"}, {"name": "attribute_set_code", "rawType": "object", "type": "string"}, {"name": "product_type", "rawType": "object", "type": "string"}, {"name": "product_websites", "rawType": "object", "type": "string"}, {"name": "product_online", "rawType": "object", "type": "string"}, {"name": "tax_class_name", "rawType": "object", "type": "string"}, {"name": "visibility", "rawType": "object", "type": "string"}, {"name": "google_product_type", "rawType": "object", "type": "string"}, {"name": "categories", "rawType": "object", "type": "string"}, {"name": "baseCat", "rawType": "object", "type": "string"}, {"name": "baseType", "rawType": "object", "type": "string"}, {"name": "manufacture_category", "rawType": "object", "type": "string"}, {"name": "producttype", "rawType": "object", "type": "string"}, {"name": "ProductName", "rawType": "object", "type": "string"}, {"name": "name", "rawType": "object", "type": "string"}, {"name": "name2", "rawType": "object", "type": "string"}, {"name": "url_key", "rawType": "object", "type": "string"}, {"name": "meta_title", "rawType": "object", "type": "string"}, {"name": "meta_description", "rawType": "object", "type": "string"}, {"name": "short_description", "rawType": "object", "type": "string"}, {"name": "LightsAmericaFinish", "rawType": "object", "type": "string"}, {"name": "manufacturer_finish", "rawType": "object", "type": "string"}, {"name": "finish", "rawType": "object", "type": "string"}, {"name": "finish_filter", "rawType": "object", "type": "string"}, {"name": "bulb_included", "rawType": "object", "type": "string"}, {"name": "number_of_bulbs", "rawType": "object", "type": "string"}, {"name": "light_count", "rawType": "object", "type": "string"}, {"name": "type_of_bulbs", "rawType": "object", "type": "string"}, {"name": "bulb_socket", "rawType": "object", "type": "string"}, {"name": "lumens", "rawType": "object", "type": "string"}, {"name": "color_temp", "rawType": "object", "type": "string"}, {"name": "cri", "rawType": "object", "type": "string"}, {"name": "wire", "rawType": "object", "type": "string"}, {"name": "chain", "rawType": "object", "type": "string"}, {"name": "rod", "rawType": "object", "type": "string"}, {"name": "height", "rawType": "object", "type": "string"}, {"name": "length", "rawType": "object", "type": "string"}, {"name": "width", "rawType": "object", "type": "string"}, {"name": "weight", "rawType": "object", "type": "string"}, {"name": "brand", "rawType": "object", "type": "string"}, {"name": "collection_name", "rawType": "object", "type": "string"}, {"name": "country_of_manufacture", "rawType": "object", "type": "string"}, {"name": "max_wattage", "rawType": "object", "type": "string"}, {"name": "voltage", "rawType": "object", "type": "string"}, {"name": "description", "rawType": "object", "type": "string"}, {"name": "style", "rawType": "object", "type": "string"}, {"name": "w_d_rated", "rawType": "object", "type": "string"}, {"name": "safety_rating", "rawType": "object", "type": "string"}, {"name": "material", "rawType": "object", "type": "string"}, {"name": "glass", "rawType": "object", "type": "string"}, {"name": "warranty", "rawType": "object", "type": "string"}, {"name": "meta_keywords", "rawType": "object", "type": "string"}, {"name": "cost", "rawType": "object", "type": "string"}, {"name": "sku_group", "rawType": "object", "type": "string"}, {"name": "rooms", "rawType": "object", "type": "string"}, {"name": "ranking", "rawType": "object", "type": "string"}, {"name": "news_from_date", "rawType": "object", "type": "string"}, {"name": "news_to_date", "rawType": "object", "type": "string"}, {"name": "Active?", "rawType": "object", "type": "string"}, {"name": "qty", "rawType": "object", "type": "string"}, {"name": "Status Date", "rawType": "object", "type": "string"}, {"name": "ShippedVia", "rawType": "object", "type": "string"}, {"name": "fan_rpm", "rawType": "object", "type": "string"}, {"name": "f_airflow", "rawType": "object", "type": "string"}, {"name": "f_efficiency", "rawType": "object", "type": "string"}, {"name": "blade_pitch", "rawType": "object", "type": "string"}, {"name": "blade_span", "rawType": "object", "type": "string"}, {"name": "blade_finish", "rawType": "object", "type": "string"}, {"name": "blade_qty", "rawType": "object", "type": "string"}, {"name": "f_speeds", "rawType": "object", "type": "string"}, {"name": "electricity_usage_watts", "rawType": "object", "type": "string"}, {"name": "fan_light_kit", "rawType": "object", "type": "string"}, {"name": "fan_downrod", "rawType": "object", "type": "string"}, {"name": "Image File Name", "rawType": "object", "type": "string"}, {"name": "Image", "rawType": "object", "type": "string"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "rawType": "object", "type": "string"}, {"name": "Small Thumbnail", "rawType": "object", "type": "string"}, {"name": "Alt Image 1", "rawType": "object", "type": "string"}, {"name": "Alt Image 2", "rawType": "object", "type": "string"}, {"name": "Alt Image 3", "rawType": "object", "type": "string"}, {"name": "Alt Image 4", "rawType": "object", "type": "string"}, {"name": "Alt Image 5", "rawType": "object", "type": "string"}, {"name": "Alt Image 6", "rawType": "object", "type": "string"}, {"name": "Alt Image 7", "rawType": "object", "type": "string"}, {"name": "Alt Image 8", "rawType": "object", "type": "string"}, {"name": "Alt Image 9", "rawType": "object", "type": "string"}, {"name": "Alt Image 10", "rawType": "object", "type": "string"}, {"name": "vendor_instock_date", "rawType": "object", "type": "string"}, {"name": "Dark_Sky", "rawType": "object", "type": "string"}, {"name": "ada", "rawType": "object", "type": "string"}, {"name": "energy_star", "rawType": "object", "type": "string"}, {"name": "glass_filter", "rawType": "object", "type": "string"}, {"name": "height_filter", "rawType": "object", "type": "string"}, {"name": "width_filter", "rawType": "object", "type": "string"}, {"name": "price_filter", "rawType": "object", "type": "string"}, {"name": "canopy", "rawType": "object", "type": "string"}, {"name": "dimmable", "rawType": "object", "type": "string"}, {"name": "shipping_width", "rawType": "object", "type": "string"}, {"name": "shipping_height", "rawType": "object", "type": "string"}, {"name": "shipping_length", "rawType": "object", "type": "string"}, {"name": "shipping_weight", "rawType": "object", "type": "string"}, {"name": "extension", "rawType": "object", "type": "string"}, {"name": "crystal", "rawType": "object", "type": "string"}, {"name": "bulb_filter", "rawType": "object", "type": "string"}, {"name": "f_watts", "rawType": "object", "type": "string"}, {"name": "airflow", "rawType": "object", "type": "string"}, {"name": "airflow_efficiency", "rawType": "object", "type": "string"}, {"name": "blade_number", "rawType": "object", "type": "string"}, {"name": "light_kit_included", "rawType": "object", "type": "string"}, {"name": "blades_reversible", "rawType": "object", "type": "string"}, {"name": "bulbs", "rawType": "object", "type": "string"}, {"name": "style_filter", "rawType": "object", "type": "string"}, {"name": "vendor_style", "rawType": "object", "type": "string"}, {"name": "dark_sky", "rawType": "object", "type": "string"}], "ref": "4e0fc075-ea52-4cbf-8456-8c6147972d1c", "rows": [["0", "N7345-790-174M", "N7345-790", "-174M", "4684194", "840254051195", "722.0", "1589.0", "Y", "1589.0", "2166.0", "1589.0", "<PERSON><PERSON><PERSON>", "simple", "base,lee", "1", "Taxable Goods", "Catalog, Search", "Pendant", "All Products/Chandeliers & Pendants,All Products/Chandeliers & Pendants/Pendants,All Products/Chandeliers & Pendants/Pendant Lights/Drum Pendants", "Pendants", "Drum Shade", "__EMPTY__VALUE__", "Drum Shade", " Pendant", "Splendour 5-Light Pendant in Aged Antique Brass", "Splendour 5-Light Pendant in Aged Antique Brass", "splendour-5-light-pendant-in-aged-antique-brass-n7345-790", "Splendour 5-Light Pendant in Aged Antique Brass", "This  from Metropolitan is part of the Splendour Collection, has 5 lights and comes in a Aged Antique Brass finish.", "This 5-Light from Metropolitan is part of the Splendour collection. It comes in a Aged Antique Brass finish.<UL><LI> Measures 20.50\" H x 24.38\" L x 24.38\" W</LI><LI>UL certified. </LI><LI>Damp rated.</LI>", "Brass - Antique", "Aged Antique Brass", "Aged Antique Brass", "Brass", "No", "5", "5-Light", "B10.5 Candelabra", "Candelabra", "", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "20.5", "24.38", "24.38", "16.82", "Metropolitan", "Splendour", "__EMPTY__VALUE__", "60.0", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "Contemporary Modern, Mid-Century Modern", "<PERSON><PERSON>", "UL", "iron / fabric", "<PERSON><PERSON><PERSON>, Iron", "__EMPTY__VALUE__", "N7345-790,840254051195,Metropolitan,All Products/Chandeliers & Pendants,All Products/Chandeliers & Pendants/Pendants,All Products/Chandeliers & Pendants/Pendant Lights/Drum Pendants, Pendant,Brass - Antique,B10.5 Candelabra,Aged Antique Brass,Splendour,Contemporary Modern, Mid-Century Modern,Damp,Iron / Fabric,", "722.0", "n7345_metropolitan-splendour-5-lt-pendant", "__EMPTY__VALUE__", "7", "06/27/2025", "08/26/2025", "Yes", "24", "6/30/2021", "UPS", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "nan", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "1157951.jpg", "https://cdn.lightsamerica.com/images/1157951.jpg", "https://cdn.lightsamerica.com/images/SM5/1157951.jpg", "https://cdn.lightsamerica.com/images/SM3/1157951.jpg", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "<PERSON>/<PERSON>", "19\" - 24\"", "19\" - 24\"", "$1500 & more", "6\"x1.13\"", "Yes", "28.63", "14.0", "28.63", "23.37", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "Incandescent/LED", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "5", "Midcentury Modern", "Contemporary Modern, Mid-Century Modern", "__EMPTY__VALUE__"]], "shape": {"columns": 124, "rows": 1}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sku</th>\n", "      <th>mpn</th>\n", "      <th>vendor_code</th>\n", "      <th>fiftytwo_id</th>\n", "      <th>upc</th>\n", "      <th>dn</th>\n", "      <th>imap</th>\n", "      <th>map_enabled</th>\n", "      <th>price</th>\n", "      <th>msrp</th>\n", "      <th>...</th>\n", "      <th>f_watts</th>\n", "      <th>airflow</th>\n", "      <th>airflow_efficiency</th>\n", "      <th>blade_number</th>\n", "      <th>light_kit_included</th>\n", "      <th>blades_reversible</th>\n", "      <th>bulbs</th>\n", "      <th>style_filter</th>\n", "      <th>vendor_style</th>\n", "      <th>dark_sky</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>N7345-790-174M</td>\n", "      <td>N7345-790</td>\n", "      <td>-174M</td>\n", "      <td>4684194</td>\n", "      <td>840254051195</td>\n", "      <td>722.0</td>\n", "      <td>1589.0</td>\n", "      <td>Y</td>\n", "      <td>1589.0</td>\n", "      <td>2166.0</td>\n", "      <td>...</td>\n", "      <td>__EMPTY__VALUE__</td>\n", "      <td>__EMPTY__VALUE__</td>\n", "      <td>__EMPTY__VALUE__</td>\n", "      <td>__EMPTY__VALUE__</td>\n", "      <td>__EMPTY__VALUE__</td>\n", "      <td>__EMPTY__VALUE__</td>\n", "      <td>5</td>\n", "      <td>Midcentury Modern</td>\n", "      <td>Contemporary Modern, Mid-Century Modern</td>\n", "      <td>__EMPTY__VALUE__</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1 rows × 124 columns</p>\n", "</div>"], "text/plain": ["              sku        mpn vendor_code fiftytwo_id           upc     dn  \\\n", "0  N7345-790-174M  N7345-790       -174M     4684194  840254051195  722.0   \n", "\n", "     imap map_enabled   price    msrp  ...           f_watts  \\\n", "0  1589.0           Y  1589.0  2166.0  ...  __EMPTY__VALUE__   \n", "\n", "            airflow airflow_efficiency      blade_number light_kit_included  \\\n", "0  __EMPTY__VALUE__   __EMPTY__VALUE__  __EMPTY__VALUE__   __EMPTY__VALUE__   \n", "\n", "  blades_reversible bulbs       style_filter  \\\n", "0  __EMPTY__VALUE__     5  Midcentury Modern   \n", "\n", "                              vendor_style          dark_sky  \n", "0  Contemporary Modern, Mid-Century Modern  __EMPTY__VALUE__  \n", "\n", "[1 rows x 124 columns]"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["df['url_key'] = df['url_key'].str.replace(\".\", \"\", regex=False)\n", "df['url_key'] = df.apply(lambda row: row['url_key'].replace(row['number_of_bulbs']+\"-light-\"+row['number_of_bulbs']+\"-light\", row['number_of_bulbs']+\"-light\"), axis=1)\n", "\n", "df['canopy'] = df['canopy'].str.replace(\"``\", '\"', regex=False)\n", "\n", "df = df[df['categories'] != \"__EMPTY__VALUE__\"]\n", "\n", "#df = df.drop(columns=['height_filter', 'width_filter'])\n", "df = df.rename(columns = {'type_of_bulbs2': 'bulb_socket'})\n", "\n", "df['brand'] = df['brand'].str.replace(\"Golden\", \"Golden Lighting\", regex=False)\n", "df['brand'] = df['brand'].str.replace(\"Oxygen\", \"Oxygen Lighting\", regex=False)\n", "\n", "df = df[df['upc'] != \"__EMPTY__VALUE__\"]\n", "\n", "df['sku_group'] = df.apply(lambda row: row['sku'] if row['sku_group'] == \"__EMPTY__VALUE__\" else row['sku_group'], axis=1)\n", "\n", "df['lumens'] = df['lumens'].str.replace(\"__EMPTY__VALUE__\", \"\")\n", "\n", "df['product_websites'] = \"base,lee\"\n", "df[\"dark_sky\"] = \"__EMPTY__VALUE__\"\n", "\n", "df.loc[df['brand'].str.lower().str.contains('quoiz'), 'product_websites'] = \"base,lee,quoizel\"\n", "\n", "# Remove bad product types\n", "df = df[(df[\"google_product_type\"] != \"Accessory\") & (df[\"google_product_type\"] != \"Ceiling Fan Control\") & (df[\"google_product_type\"] != \"Under Cabinet\")]\n", "\n", "# Fixing weigth column wi\n", "df['weight'] = df['weight'].apply(lambda row: \"__EMPTY__VALUE__\" if row == \"\" else row)\n", "df['weight'] = df['weight'].apply(lambda row: \"__EMPTY__VALUE__\" if row == \"nan\" else row)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["BRAND NAMES CLEANING"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["df['brand'] = df['brand'].str.replace(\"Access\", \"Access Lighting\").str.replace(\"Artcraft\", \"Artcraft Lighting\").str.replace(\"Currey and Company\", \"Currey & Company\").str.replace(\"<PERSON>s\", \"Dals Lighting\").str.replace(\"ET2\", \"ET2 Lighting\").str.replace(\"Hunter\", \"Hunter Fans\").str.replace(\"Kuzco Lighting\", \"Kuzco\").str.replace(\"Maxim\", \"Maxim Lighting\").str.replace(\"Millennium\", \"Millennium Lighting\").str.replace(\"Modern Forms Fans\", \"Modern Forms\").str.replace(\"Schonbek Beyond\", \"Schonbek\").str.replace(\"Schonbek Forever\", \"Schonbek\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["STOP AND SAVE IF THIS IS FOR LO-SITE, IF IS FOR PL JUST CONTINUE THE NEXT LINE"]}, {"cell_type": "markdown", "metadata": {}, "source": ["PL CATEGORIES BULDING"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["df['categories'] = \"\"\n", "\n", "# *Accessories*\n", "df.loc[(df['google_product_type'].str.contains('Accessory')) | (df['google_product_type'].str.contains('Chime')), 'categories'] = \"All Products/Accessories\"\n", "\n", "# *Bath*\n", "df.loc[df['google_product_type'].str.contains('Bath'), 'categories'] = \"All Products/Bath\"\n", "df.loc[(df['google_product_type'].str.contains('Bath')) & (df['name'].str.contains('Vanity')), 'categories'] += \",All Products/Bath/Bathroom Vanity Lights\"\n", "df.loc[(df['google_product_type'].str.contains('Bath')) & ((df['name'].str.contains('Flush')) | (df['name'].str.contains('Ceiling'))), 'categories'] += \",All Products/Bath/Bathroom Ceiling Lights\"\n", "df.loc[(df['google_product_type'].str.contains('Bath')) & ((df['name'].str.contains('Wall')) | (df['name'].str.contains('Sconce'))), 'categories'] += \",All Products/Bath/Bathroom Wall Sconces\"\n", "\n", "# *Fans*\n", "df.loc[df['google_product_type'].str.contains('Fandelier'), 'categories'] = \"All Products/Fans,All Products/Fans/Fandeliers\"\n", "df.loc[df['google_product_type'].str.contains('Hanging Ceiling Fan'), 'categories'] = \"All Products/Fans,All Products/Fans/Ceiling Fans\"\n", "df.loc[(df['google_product_type'].str.contains('Fan')) & (df['w_d_rated'].str.contains('Damp')), 'categories'] += \",All Products/Fans/Damp Location\"\n", "df.loc[(df['google_product_type'].str.contains('Fan')) & (df['w_d_rated'].str.contains('Wet')), 'categories'] += \",All Products/Fans/Wet Location\"\n", "df.loc[df['google_product_type'].str.contains('Fan Control'), 'categories'] = \"All Products/Fans,All Products/Fans/Fan Controls\"\n", "df.loc[df['google_product_type'].str.contains('Hugger'), 'categories'] = \"All Products/Fans,All Products/Fans/Hugger Mounts\"\n", "df.loc[df['google_product_type'].str.contains('Wall Fan'), 'categories'] = \"All Products/Fans,All Products/Fans/Portable & Wall Fans\"\n", "df.loc[(df['google_product_type'].str.contains('Light Kit')) | (df['name'].str.contains('Fitter')), 'categories'] = \"All Products/Fans,All Products/Fans/Light Kits & Fitters\"\n", "df.loc[df['google_product_type'].str.contains('Blade'), 'categories'] = \"All Products/Fans,All Products/Fans/Fan Blades\"\n", "df.loc[df['google_product_type'].str.contains('Downrod'), 'categories'] = \"All Products/Fans,All Products/Fans/Fan Accessories\"\n", "\n", "# *Ceiling Lights*\n", "df.loc[df['google_product_type'].str.contains('Flush Mount'), 'categories'] = 'All Products/Ceiling Lights,All Products/Ceiling Lights/Flush Mount Ceiling Lights'\n", "df.loc[df['google_product_type'].str.contains('Semi-Flush'), 'categories'] = 'All Products/Ceiling Lights,All Products/Ceiling Lights/Semi-Flush Ceiling Lights'\n", "df.loc[df['google_product_type'].str.contains('Track'), 'categories'] = 'All Products/Ceiling Lights,All Products/Ceiling Lights/Track Lighting'\n", "df.loc[df['google_product_type'].str.contains('Recessed'), 'categories'] = 'All Products/Ceiling Lights,All Products/Ceiling Lights/Recessed Lighting'\n", "df.loc[(df['categories'].str.contains('Ceiling Lights')) & (df['type_of_bulbs'].str.contains('LED')), 'categories'] = df['categories'] + ',All Products/Ceiling Lights/LED Ceiling Lights'\n", "\n", "# *Chandeliers & Pendants*\n", "df.loc[df['google_product_type'].str.contains('Chandelier'), 'categories'] = 'All Products/Chandeliers & Pendants,All Products/Chandeliers & Pendants/Chandeliers'\n", "df.loc[df['google_product_type'].str.contains('Pendant'), 'categories'] = 'All Products/Chandeliers & Pendants,All Products/Chandeliers & Pendants/Pendants'\n", "df.loc[df['google_product_type'].str.contains('Linear'), 'categories'] = df['categories'] + ',All Products/Chandeliers & Pendants/Chandeliers/Linear Chandeliers'\n", "df.loc[df['google_product_type'].str.contains('Outdoor Chandelier'), 'categories'] = df['categories'] + ',All Products/Chandeliers & Pendants/Chandeliers/Outdoor Chandeliers'\n", "df.loc[(df['google_product_type'].str.contains('Mini Chandelier')) | (df['product_type'].str.contains('Mini Chand')), 'categories'] = df['categories'] + ',All Products/Chandeliers & Pendants/Chandeliers/Mini Chandeliers'\n", "df.loc[(df['google_product_type'].str.contains('Pendant')) & (df['product_type'].str.contains('Drum')), 'categories'] = df['categories'] + ',All Products/Chandeliers & Pendants/Pendants/Drum Pendants'\n", "df.loc[(df['google_product_type'].str.contains('Pendant')) & (df['product_type'].str.contains('Bowl')), 'categories'] = df['categories'] + ',All Products/Chandeliers & Pendants/Pendants/Bowl Pendants'\n", "df.loc[df['google_product_type'].str.contains('Mini Pendant'), 'categories'] = df['categories'] + ',All Products/Chandeliers & Pendants/Pendants/Mini Pendants'\n", "\n", "# *Wall Lights*\n", "df.loc[(df['google_product_type'] == 'Wall Light') | (df['google_product_type'] == 'Wall Lamp'), 'categories'] = 'All Products/Wall Lights,All Products/Wall Lights/Wall Sconces'\n", "df.loc[df['google_product_type'].str.contains('Under'), 'categories'] = 'All Products/Wall Lights,All Products/Wall Lights/Under Cabinet Lights'\n", "df.loc[df['google_product_type'].str.contains('Picture'), 'categories'] = 'All Products/Wall Lights,All Products/Wall Lights/Picture Lights'\n", "df.loc[(df['google_product_type'].str.contains('Wall Lights')) & (df['type_of_bulbs'].str.contains('LED')), 'categories'] = df['categories'] + ',All Products/Wall Lights/LED Wall Lights'\n", "\n", "# *Outdoor*\n", "df.loc[df['google_product_type'].str.contains('Outdoor Wall'), 'categories'] = 'All Products/Outdoor Lights,All Products/Outdoor Lights/Outdoor Wall Lights'\n", "df.loc[df['google_product_type'].str.contains('Outdoor Hanging'), 'categories'] = 'All Products/Outdoor Lights,All Products/Outdoor Lights/Outdoor Hanging Lights'\n", "df.loc[df['google_product_type'].str.contains('Post'), 'categories'] = 'All Products/Outdoor Lights,All Products/Outdoor Lights/Outdoor Post & Pier Lights'\n", "df.loc[df['google_product_type'].str.contains('Outdoor Ceiling'), 'categories'] = 'All Products/Outdoor Lights,All Products/Outdoor Lights/Outdoor Ceiling Lights'\n", "df.loc[(df['name'].str.contains('Address')) | (df['name'].str.contains('Outdoor Portable')) | (df['name'].str.contains('Speaker')) | ((df['name'].str.contains('Outdoor')) & (df['name'].str.contains('Lamp'))), 'categories'] = 'All Products/Outdoor Lights,All Products/Outdoor Lights/Outdoor Decor'\n", "df.loc[(df['name'].str.contains('Rock')) | (df['name'].str.contains('String')), 'categories'] = 'All Products/Outdoor Lights,All Products/Outdoor Lights/Outdoor Decor'\n", "df.loc[(df['name'].str.contains('Outdoor Wall Lamp')) & (df['categories'].str.contains('Decor')), 'categories'] = 'All Products/Outdoor Lights,All Products/Outdoor Lights/Outdoor Wall Lights'\n", "\n", "# *Flood*\n", "df.loc[df['google_product_type'].str.contains('Flood'), 'categories'] = 'All Products/Outdoor Lights,All Products/Outdoor Lights/Outdoor Wall Lights'\n", "\n", "# *Landscape*\n", "df.loc[df['google_product_type'].str.contains('Landscape'), 'categories'] = 'All Products/Outdoor Lights/Landscape Lights'\n", "df.loc[(df['google_product_type'].str.contains('Deck')) | (df['google_product_type'].str.contains('Step')), 'categories'] = 'All Products/Outdoor Lights/Landscape Lights,All Products/Outdoor Lights/Landscape Lights/Deck & Step Lights'\n", "df.loc[df['google_product_type'].str.contains('Path'), 'categories'] = 'All Products/Outdoor Lights/Landscape Lights,All Products/Outdoor Lights/Landscape Lights/Pathway Lights'\n", "\n", "# *Lamps*\n", "df.loc[df['google_product_type'].str.contains('Table Lamp'), 'categories'] = 'All Products/Lamps,All Products/Lamps/Table Lamps'\n", "df.loc[df['google_product_type'].str.contains('Floor Lamp'), 'categories'] = 'All Products/Lamps,All Products/Lamps/Floor Lamps'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["LEE SITE UPDATE FILE BEFORE SAVE THE FINAL DF"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_temp = df\n", "df_temp = df_temp.replace(\"__EMPTY__VALUE__\", \"\")\n", "df_temp.insert(1, \"store_view_code\", \"lee\")\n", "df_temp.insert(2, \"meta_keyword\", \"__EMPTY__VALUE__\")\n", "\n", "df_temp[\"name\"] = df_temp.apply(lambda row: row[\"collection_name\"] + \" \" + row[\"ProductName\"] + \" in \" + row[\"manufacturer_finish\"] + \" by \" + row[\"brand\"], axis=1)\n", "df_temp[\"name\"] = df_temp[\"name\"].apply(lambda row: str(row).replace(\"by\", \"\") if str(row).endswith(\"by\") else row)\n", "df_temp['name'] = df_temp['name'].str.replace(\" w\", \"\").str.replace(\"/\", \" \").str.replace(\"/\", \" \").str.replace(\"Bathroom Bathroom\", \"Bathroom\").str.replace(\"Bathroom Vanity Lightroom Bathroom Vanity Light\", \"Bathroom Vanity Light\").str.replace(\"Bathroom Vanity Light Bathroom Vanity Light\", \"Bathroom Vanity Light\").str.replace('\"Ceiling', '\" Ceiling')\n", "\n", "\n", "df_temp[\"meta_title\"] = df_temp.apply(lambda row: row[\"google_product_type\"] + \" | \" + str(row[\"brand\"]) + \" \" + row[\"ProductName\"] + \" \" + row[\"mpn\"], axis=1)\n", "df_temp[\"meta_title\"] = df_temp[\"meta_title\"].apply(lambda row: str(row).replace(\" | \", \"\") if str(row).startswith(\" | \") else row)\n", "\n", "df_temp[\"meta_description\"] = df_temp.apply(lambda row: \"This \" + row[\"style\"].split(\",\")[0].lower() + \" style \" + row[\"ProductName\"] + \", part of the \" + row[\"collection_name\"] + \" collection\" + \", from \" + row[\"brand\"] + \", comes in a \" + row[\"manufacturer_finish\"] + \" finish.\", axis=1)\n", "\n", "\n", "df_temp['description'] = df_temp.apply(lambda row: create_description(row[\"name\"], row[\"brand\"], row[\"collection_name\"], row[\"style\"], row[\"manufacturer_finish\"]), axis = 1)\n", "df_temp['description'] = df_temp['description'].str.replace(\",\", \", \").str.replace(\"  \", \" \").str.replace(\".,\", \",\")\n", "df_temp['description'] = df_temp['description'].str.replace(r'([a-z])([A-Z])', r'\\1 \\2', regex=True)\n", "df_temp['description'] = df_temp['description'].str.replace(r'([A-Z])([A-Z])', r'\\1 \\2', regex=True)\n", "df_temp[\"description\"] = df_temp[\"description\"].str.replace(\"L ED\", \"LED\").str.replace(\"``\", \"\").str.replace(\"collectionfinishing\", \"collection finishing\")\n", "df_temp[\"description\"] = df_temp[\"description\"].str.replace(\"C WI\", \"CWI\").str.replace(\"/\", \" \").str.replace(\"Eurofase\", \"Eurofase \").str.replace(\" ,\", \",\").str.replace(\" .\", \".\")\n", "df_temp[\"description\"] = df_temp[\"description\"].str.replace(\"byEurofase\", \"by Eurofase\").str.replace(\"/\", \" \").str.replace(\"Eurofase\", \"Eurofase \").str.replace(\" ,\", \",\")\n", "df_temp['description'] = df_temp['description'].apply(lambda row: row + '.' if not row.endswith('.') else row)\n", "\n", "\n", "df_temp = df_temp[[\"sku\", \"store_view_code\", \"meta_keyword\", \"name\", \"meta_title\", \"description\", \"meta_description\"]]\n", "\n", "# Save the updated LEE FILE\n", "df_temp.to_csv(r\"C:\\Users\\<USER>\\Desktop\\New_To_Add_MAGENTO\\PL\\LEE_SEO_All_brands_JUNE_update_2.csv\", index = False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["SAVE THE FINAL PRODUCT DATA DF FOR PL"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "sku", "rawType": "object", "type": "string"}, {"name": "mpn", "rawType": "object", "type": "string"}, {"name": "fiftytwo_id", "rawType": "object", "type": "string"}, {"name": "upc", "rawType": "object", "type": "string"}, {"name": "dn", "rawType": "object", "type": "string"}, {"name": "imap", "rawType": "object", "type": "string"}, {"name": "price", "rawType": "object", "type": "string"}, {"name": "msrp", "rawType": "object", "type": "string"}, {"name": "map_price", "rawType": "object", "type": "string"}, {"name": "map_enabled", "rawType": "object", "type": "string"}, {"name": "price_filter", "rawType": "object", "type": "string"}, {"name": "safety_rating", "rawType": "object", "type": "string"}, {"name": "attribute_set_code", "rawType": "object", "type": "string"}, {"name": "product_type", "rawType": "object", "type": "string"}, {"name": "product_websites", "rawType": "object", "type": "string"}, {"name": "product_online", "rawType": "object", "type": "string"}, {"name": "tax_class_name", "rawType": "object", "type": "string"}, {"name": "visibility", "rawType": "object", "type": "string"}, {"name": "google_product_type", "rawType": "object", "type": "string"}, {"name": "categories", "rawType": "object", "type": "string"}, {"name": "name", "rawType": "object", "type": "string"}, {"name": "url_key", "rawType": "object", "type": "string"}, {"name": "meta_title", "rawType": "object", "type": "string"}, {"name": "meta_description", "rawType": "object", "type": "string"}, {"name": "short_description", "rawType": "object", "type": "string"}, {"name": "description", "rawType": "object", "type": "string"}, {"name": "brand", "rawType": "object", "type": "string"}, {"name": "number_of_bulbs", "rawType": "object", "type": "string"}, {"name": "bulb_filter", "rawType": "object", "type": "string"}, {"name": "type_of_bulbs", "rawType": "object", "type": "string"}, {"name": "bulb_included", "rawType": "object", "type": "string"}, {"name": "collection_name", "rawType": "object", "type": "string"}, {"name": "country_of_manufacture", "rawType": "object", "type": "string"}, {"name": "finish", "rawType": "object", "type": "string"}, {"name": "manufacturer_finish", "rawType": "object", "type": "string"}, {"name": "finish_filter", "rawType": "object", "type": "string"}, {"name": "max_wattage", "rawType": "object", "type": "string"}, {"name": "voltage", "rawType": "object", "type": "string"}, {"name": "weight", "rawType": "object", "type": "string"}, {"name": "length", "rawType": "object", "type": "string"}, {"name": "width", "rawType": "object", "type": "string"}, {"name": "height", "rawType": "object", "type": "string"}, {"name": "style", "rawType": "object", "type": "string"}, {"name": "style_filter", "rawType": "object", "type": "string"}, {"name": "vendor_style", "rawType": "object", "type": "string"}, {"name": "w_d_rated", "rawType": "object", "type": "string"}, {"name": "material", "rawType": "object", "type": "string"}, {"name": "meta_keywords", "rawType": "object", "type": "string"}, {"name": "light_count", "rawType": "object", "type": "string"}, {"name": "cost", "rawType": "object", "type": "string"}, {"name": "sku_group", "rawType": "object", "type": "string"}, {"name": "blade_finish", "rawType": "object", "type": "string"}, {"name": "blade_pitch", "rawType": "object", "type": "string"}, {"name": "f_airflow", "rawType": "object", "type": "string"}, {"name": "airflow", "rawType": "object", "type": "string"}, {"name": "blades_reversible", "rawType": "object", "type": "string"}, {"name": "f_watts", "rawType": "object", "type": "string"}, {"name": "electricity_usage_watts", "rawType": "object", "type": "string"}, {"name": "glass", "rawType": "object", "type": "string"}, {"name": "glass_filter", "rawType": "object", "type": "string"}, {"name": "color_temp", "rawType": "object", "type": "string"}, {"name": "cri", "rawType": "object", "type": "string"}, {"name": "lumens", "rawType": "object", "type": "string"}, {"name": "wire", "rawType": "object", "type": "string"}, {"name": "chain", "rawType": "object", "type": "string"}, {"name": "rod", "rawType": "object", "type": "string"}, {"name": "canopy", "rawType": "object", "type": "string"}, {"name": "dimmable", "rawType": "object", "type": "string"}, {"name": "warranty", "rawType": "object", "type": "string"}, {"name": "f_efficiency", "rawType": "object", "type": "string"}, {"name": "airflow_efficiency", "rawType": "object", "type": "string"}, {"name": "bulb_socket", "rawType": "object", "type": "string"}, {"name": "blade_span", "rawType": "object", "type": "string"}, {"name": "blade_qty", "rawType": "object", "type": "string"}, {"name": "blade_number", "rawType": "object", "type": "string"}, {"name": "f_speeds", "rawType": "object", "type": "string"}, {"name": "light_kit_included", "rawType": "object", "type": "string"}, {"name": "fan_light_kit", "rawType": "object", "type": "string"}, {"name": "fan_downrod", "rawType": "object", "type": "string"}, {"name": "ada", "rawType": "object", "type": "string"}, {"name": "energy_star", "rawType": "object", "type": "string"}, {"name": "dark_sky", "rawType": "object", "type": "string"}, {"name": "news_from_date", "rawType": "object", "type": "string"}, {"name": "news_to_date", "rawType": "object", "type": "string"}], "ref": "5dfe6f1d-ec2a-424e-a8bc-17390a103e1f", "rows": [["0", "N7345-790-174M", "N7345-790", "4684194", "840254051195", "722.0", "1589.0", "1589.0", "2166.0", "1589.0", "Y", "$1500 & more", "UL", "<PERSON><PERSON><PERSON>", "simple", "base,lee", "1", "Taxable Goods", "Catalog, Search", "Pendant", "All Products/Chandeliers & Pendants,All Products/Chandeliers & Pendants/Pendants", "Splendour 5-Light Pendant in Aged Antique Brass", "splendour-5-light-pendant-in-aged-antique-brass-n7345-790", "Splendour 5-Light Pendant in Aged Antique Brass", "This  from Metropolitan is part of the Splendour Collection, has 5 lights and comes in a Aged Antique Brass finish.", "This 5-Light from Metropolitan is part of the Splendour collection. It comes in a Aged Antique Brass finish.<UL><LI> Measures 20.50\" H x 24.38\" L x 24.38\" W</LI><LI>UL certified. </LI><LI>Damp rated.</LI>", "__EMPTY__VALUE__", "Metropolitan", "5", "Incandescent/LED", "B10.5 Candelabra", "No", "Splendour", "__EMPTY__VALUE__", "Aged Antique Brass", "Aged Antique Brass", "Brass", "60.0", "__EMPTY__VALUE__", "16.82", "24.38", "24.38", "20.5", "Contemporary Modern, Mid-Century Modern", "Midcentury Modern", "Contemporary Modern, Mid-Century Modern", "<PERSON><PERSON>", "iron / fabric", "N7345-790,840254051195,Metropolitan,All Products/Chandeliers & Pendants,All Products/Chandeliers & Pendants/Pendants,All Products/Chandeliers & Pendants/Pendant Lights/Drum Pendants, Pendant,Brass - Antique,B10.5 Candelabra,Aged Antique Brass,Splendour,Contemporary Modern, Mid-Century Modern,Damp,Iron / Fabric,", "5-Light", "722.0", "n7345_metropolitan-splendour-5-lt-pendant", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "<PERSON><PERSON><PERSON>, Iron", "<PERSON>/<PERSON>", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "6\"x1.13\"", "Yes", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "Candelabra", "nan", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "__EMPTY__VALUE__", "06/27/2025", "08/26/2025"]], "shape": {"columns": 84, "rows": 1}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sku</th>\n", "      <th>mpn</th>\n", "      <th>fiftytwo_id</th>\n", "      <th>upc</th>\n", "      <th>dn</th>\n", "      <th>imap</th>\n", "      <th>price</th>\n", "      <th>msrp</th>\n", "      <th>map_price</th>\n", "      <th>map_enabled</th>\n", "      <th>...</th>\n", "      <th>blade_number</th>\n", "      <th>f_speeds</th>\n", "      <th>light_kit_included</th>\n", "      <th>fan_light_kit</th>\n", "      <th>fan_downrod</th>\n", "      <th>ada</th>\n", "      <th>energy_star</th>\n", "      <th>dark_sky</th>\n", "      <th>news_from_date</th>\n", "      <th>news_to_date</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>N7345-790-174M</td>\n", "      <td>N7345-790</td>\n", "      <td>4684194</td>\n", "      <td>840254051195</td>\n", "      <td>722.0</td>\n", "      <td>1589.0</td>\n", "      <td>1589.0</td>\n", "      <td>2166.0</td>\n", "      <td>1589.0</td>\n", "      <td>Y</td>\n", "      <td>...</td>\n", "      <td>__EMPTY__VALUE__</td>\n", "      <td>__EMPTY__VALUE__</td>\n", "      <td>__EMPTY__VALUE__</td>\n", "      <td>__EMPTY__VALUE__</td>\n", "      <td>__EMPTY__VALUE__</td>\n", "      <td>__EMPTY__VALUE__</td>\n", "      <td>__EMPTY__VALUE__</td>\n", "      <td>__EMPTY__VALUE__</td>\n", "      <td>06/27/2025</td>\n", "      <td>08/26/2025</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1 rows × 84 columns</p>\n", "</div>"], "text/plain": ["              sku        mpn fiftytwo_id           upc     dn    imap   price  \\\n", "0  N7345-790-174M  N7345-790     4684194  840254051195  722.0  1589.0  1589.0   \n", "\n", "     msrp map_price map_enabled  ...      blade_number          f_speeds  \\\n", "0  2166.0    1589.0           Y  ...  __EMPTY__VALUE__  __EMPTY__VALUE__   \n", "\n", "  light_kit_included     fan_light_kit       fan_downrod               ada  \\\n", "0   __EMPTY__VALUE__  __EMPTY__VALUE__  __EMPTY__VALUE__  __EMPTY__VALUE__   \n", "\n", "        energy_star          dark_sky news_from_date news_to_date  \n", "0  __EMPTY__VALUE__  __EMPTY__VALUE__     06/27/2025   08/26/2025  \n", "\n", "[1 rows x 84 columns]"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["# KEEP ONLY NEEDED COLUMNS\n", "df = df[['sku', 'mpn', 'fiftytwo_id', 'upc', 'dn', 'imap', 'price', 'msrp', 'map_price', 'map_enabled', 'price_filter', 'safety_rating', 'attribute_set_code', 'product_type', 'product_websites', 'product_online', 'tax_class_name', 'visibility', 'google_product_type', 'categories', 'name', 'url_key', 'meta_title', 'meta_description', 'short_description', 'description', 'brand', 'number_of_bulbs', 'bulb_filter', 'type_of_bulbs', 'bulb_included', 'collection_name', 'country_of_manufacture', 'finish', 'manufacturer_finish', 'finish_filter', 'max_wattage', 'voltage', 'weight', 'length', 'width', 'height', 'style', 'style_filter', 'vendor_style', 'w_d_rated', 'material', 'meta_keywords', 'light_count', 'cost', 'sku_group', 'ranking', 'blade_finish', 'blade_pitch', 'f_airflow', 'airflow', 'blades_reversible', 'f_watts', 'electricity_usage_watts', 'glass', 'glass_filter', 'color_temp', 'cri', 'lumens', 'wire', 'chain', 'rod', 'canopy', 'dimmable', 'warranty', 'f_efficiency', 'airflow_efficiency', 'bulb_socket', 'blade_span', 'blade_qty', 'blade_number', 'f_speeds', 'light_kit_included', 'fan_light_kit', 'fan_downrod', 'ada', 'energy_star', 'dark_sky', 'news_from_date', 'news_to_date']]\n", "df = df.drop(columns = \"ranking\") # ONLY FOR PL\n", "\n", "# Save the cleaned DataFrame to a new CSV file\n", "df.to_csv(r\"C:\\Users\\<USER>\\Desktop\\New_To_Add_MAGENTO\\PL\\PL_All_brands_JUNE_update_2_CLEANED.csv\", index = False)\n", "df"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 2}